/**
 * Production-ready Preview Panel Component
 * Handles code preview, iframe rendering, and view mode switching
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { FiCode, FiEye, FiRefreshCw, FiMaximize2, FiMinimize2 } from 'react-icons/fi';
import { ViewMode } from '../../hooks/useEditorV3';
import CodeEditor from '../CodeEditor';

// ============================================================================
// TYPES
// ============================================================================

interface PreviewPanelProps {
  htmlContent: string;
  streamingContent: string;
  stableIframeContent: string;
  viewMode: ViewMode;
  isGenerating: boolean;
  onViewModeChange: (mode: ViewMode) => void;
  onElementClick?: (element: any) => void;
  onHtmlChange?: (html: string) => void; // For code editing
  className?: string;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const extractHtmlFromResponse = (response: string): string => {
  if (!response) return '';

  let htmlFragment = '';

  // First, check if this is already a complete HTML document
  if (response.trim().startsWith('<!DOCTYPE html') || response.trim().startsWith('<html')) {
    return response.trim();
  }

  // Look for HTML content between ```html and ``` markers
  const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
  if (htmlMatch) {
    htmlFragment = htmlMatch[1].trim();
  } else if (response.includes('<') && response.includes('>')) {
    // If response contains HTML tags, assume it's HTML fragment
    const firstTagIndex = response.indexOf('<');
    htmlFragment = response.substring(firstTagIndex).trim();
  } else {
    return response;
  }

  // If it's a fragment (doesn't start with DOCTYPE or html), wrap it in parent structure
  if (htmlFragment && !htmlFragment.startsWith('<!DOCTYPE') && !htmlFragment.startsWith('<html')) {
    return createParentHtmlStructure(htmlFragment);
  }

  return htmlFragment;
};

// Create parent HTML structure with Tailwind CSS for fragments
const createParentHtmlStructure = (fragment: string): string => {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Event delegation for data-action attributes
    document.addEventListener('DOMContentLoaded', function() {
      document.addEventListener('click', function(e) {
        const target = e.target.closest('[data-action]');
        if (target) {
          const action = target.getAttribute('data-action');
          const targetId = target.getAttribute('data-target');

          switch(action) {
            case 'openModal':
              if (targetId) openModal(targetId);
              break;
            case 'closeModal':
              if (targetId) closeModal(targetId);
              break;
            default:
              console.log('Action triggered:', action, 'Target:', targetId);
          }
        }
      });
    });

    // Modal functions
    function openModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
      }
    }

    function closeModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
      }
    }
  </script>
</head>
<body>
  <div id="app">
    ${fragment}
  </div>
</body>
</html>`;
};

// ============================================================================
// SUB-COMPONENTS
// ============================================================================

interface ViewModeToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
}

const ViewModeToggle: React.FC<ViewModeToggleProps> = ({ viewMode, onViewModeChange }) => (
  <div className="flex bg-gray-100 rounded-lg p-1">
    <button
      onClick={() => onViewModeChange('preview')}
      className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
        viewMode === 'preview'
          ? 'bg-white text-gray-900 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'
      }`}
    >
      <FiEye className="w-4 h-4 mr-1.5" />
      Preview
    </button>
    <button
      onClick={() => onViewModeChange('code')}
      className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
        viewMode === 'code'
          ? 'bg-white text-gray-900 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'
      }`}
    >
      <FiCode className="w-4 h-4 mr-1.5" />
      Code
    </button>
  </div>
);

interface PreviewIframeProps {
  content: string;
  isGenerating: boolean;
  onElementClick?: (element: any) => void;
}

const PreviewIframe: React.FC<PreviewIframeProps> = ({
  content,
  isGenerating,
  onElementClick
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [scriptInjected, setScriptInjected] = useState(false);

  // Readdy.ai style interaction detection script
  const INTERACTION_DETECTION_SCRIPT = `
    (function() {
      console.log('🔥 Readdy-style interaction detection script loaded');

      function needsImplementation(element) {
        if (!element || !element.tagName) return { needs: false };

        const tagName = element.tagName.toLowerCase();
        const text = element.textContent?.trim() || '';

        // Check buttons without onclick handlers
        if (tagName === 'button' ||
            (tagName === 'input' && (element.type === 'button' || element.type === 'submit')) ||
            element.classList.contains('btn')) {

          const hasOnclick = element.onclick || element.getAttribute('onclick');
          console.log(\`🔍 Checking button "\${text}" - onclick: \${hasOnclick ? 'YES' : 'NO'}\`);

          if (!hasOnclick) {
            console.log(\`✅ Button "\${text}" needs implementation!\`);
            return {
              needs: true,
              type: 'button',
              reason: 'Button needs click functionality',
              text: text
            };
          }
        }

        // Check links without href or with placeholder href
        if (tagName === 'a') {
          const href = element.getAttribute('href');
          if (!href || href === '#' || href === 'javascript:void(0)') {
            console.log(\`✅ Link "\${text}" needs implementation!\`);
            return {
              needs: true,
              type: 'link',
              reason: 'Link needs destination',
              text: text
            };
          }
        }

        return { needs: false };
      }

      function addUnimplementedIndicators() {
        console.log('🔥 Adding unimplemented indicators...');

        // Add CSS for indicators
        if (!document.getElementById('unimplemented-styles')) {
          const style = document.createElement('style');
          style.id = 'unimplemented-styles';
          style.textContent = \`
            .unimplemented-indicator {
              position: relative !important;
              cursor: pointer !important;
            }
            .unimplemented-indicator::after {
              content: '⚡';
              position: absolute;
              top: -8px;
              right: -8px;
              background: #ff6b35;
              color: white;
              border-radius: 50%;
              width: 16px;
              height: 16px;
              font-size: 10px;
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 1000;
              pointer-events: none;
              box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            .unimplemented-indicator:hover::after {
              background: #ff4500;
              transform: scale(1.1);
              transition: all 0.2s ease;
            }
          \`;
          document.head.appendChild(style);
        }

        // Find and mark unimplemented elements
        const allElements = document.querySelectorAll('*');
        let indicatorCount = 0;

        allElements.forEach(element => {
          const check = needsImplementation(element);
          if (check.needs) {
            element.classList.add('unimplemented-indicator');
            indicatorCount++;

            // Add click handler for implementation modal
            element.addEventListener('click', function(e) {
              e.preventDefault();
              e.stopPropagation();

              console.log('🔥 Unimplemented element clicked:', check);

              // Send message to parent window
              window.parent.postMessage({
                type: 'ELEMENT_CLICKED',
                element: {
                  textContent: check.text,
                  implementationType: check.type,
                  implementationReason: check.reason
                }
              }, '*');
            });
          }
        });

        console.log(\`🔥 Added \${indicatorCount} unimplemented indicators\`);

        // Debug: List all buttons found
        const allButtons = document.querySelectorAll('button');
        console.log(\`🔍 Debug: Found \${allButtons.length} total buttons:\`);
        allButtons.forEach((btn, i) => {
          const hasOnclick = btn.onclick || btn.getAttribute('onclick');
          console.log(\`  Button \${i + 1}: "\${btn.textContent?.trim()}" - onclick: \${hasOnclick ? 'YES' : 'NO'}\`);
        });
      }

      // Make functions globally available
      window.addUnimplementedIndicators = addUnimplementedIndicators;
      window.refreshUnimplementedIndicators = addUnimplementedIndicators;

      // Initialize when DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addUnimplementedIndicators);
      } else {
        addUnimplementedIndicators();
      }

      // Also run after a delay to catch dynamic content
      setTimeout(addUnimplementedIndicators, 500);
    })();
  `;

  // Inject script into iframe (Readdy.ai style)
  const injectInteractionScript = useCallback(() => {
    if (!iframeRef.current) return false;

    const iframe = iframeRef.current;
    if (!iframe?.contentDocument) return false;

    const doc = iframe.contentDocument;

    // Check if script is already injected
    if (doc.querySelector('#interaction-detection-script')) return true;

    try {
      // Create and inject script element
      const script = doc.createElement('script');
      script.id = 'interaction-detection-script';
      script.textContent = INTERACTION_DETECTION_SCRIPT;
      doc.body.appendChild(script);

      return true;
    } catch (error) {
      return false;
    }
  }, [INTERACTION_DETECTION_SCRIPT]);

  // Handle iframe load event (Readdy.ai approach)
  useEffect(() => {
    if (!iframeRef.current) return;

    const handleIframeLoad = () => {
      console.log('🔥 Iframe loaded, injecting interaction script...');
      const success = injectInteractionScript();
      setScriptInjected(success);
    };

    const currentIframe = iframeRef.current;
    currentIframe.addEventListener('load', handleIframeLoad);

    // If iframe is already loaded, inject immediately
    if (currentIframe.contentDocument?.readyState === 'complete') {
      const success = injectInteractionScript();
      setScriptInjected(success);
    }

    return () => {
      currentIframe.removeEventListener('load', handleIframeLoad);
    };
  }, [injectInteractionScript]);

  // Handle iframe messages
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'ELEMENT_CLICKED') {
        onElementClick?.(event.data.element);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [onElementClick]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : 'h-full'}`}>
      {/* Iframe Controls */}
      <div className="absolute top-2 right-2 z-10 flex space-x-2">
        <button
          onClick={() => iframeRef.current?.contentWindow?.location.reload()}
          className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm hover:bg-white transition-colors"
          title="Refresh preview"
        >
          <FiRefreshCw className="w-4 h-4 text-gray-600" />
        </button>
        <button
          onClick={toggleFullscreen}
          className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm hover:bg-white transition-colors"
          title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        >
          {isFullscreen ? (
            <FiMinimize2 className="w-4 h-4 text-gray-600" />
          ) : (
            <FiMaximize2 className="w-4 h-4 text-gray-600" />
          )}
        </button>
      </div>

      {/* Loading Overlay */}
      {isGenerating && (
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="flex items-center space-x-3 bg-white rounded-lg px-4 py-3 shadow-lg">
            <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-600 border-t-transparent"></div>
            <span className="text-sm font-medium text-gray-900">Generating...</span>
          </div>
        </div>
      )}

      {/* Iframe */}
      <div className="relative w-full h-full">
        {isGenerating && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-20">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent mb-4"></div>
              <span className="text-base font-medium text-gray-900">Generating preview...</span>
            </div>
          </div>
        )}
        <iframe
          ref={iframeRef}
          srcDoc={content}
          className="w-full h-full border-0 bg-white"
          title="Preview"
          sandbox="allow-scripts allow-same-origin allow-modals allow-popups allow-forms"
        />
        {/* Debug info */}
        {scriptInjected && (
          <div className="absolute bottom-2 left-2 bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
            ⚡ Interaction detection active
          </div>
        )}
      </div>
    </div>
  );
};

interface CodeViewProps {
  content: string;
  isGenerating: boolean;
  onContentChange?: (content: string) => void;
}

const CodeView: React.FC<CodeViewProps> = ({ content, isGenerating, onContentChange }) => {
  return (
    <div className="relative h-full">
      {/* Loading Overlay */}
      {isGenerating && (
        <div className="absolute inset-0 bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-10">
          <div className="flex items-center space-x-3 bg-gray-800 rounded-lg px-4 py-3">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-400 border-t-transparent"></div>
            <span className="text-sm text-gray-300">Generating code...</span>
          </div>
        </div>
      )}

      {/* Enhanced Code Editor */}
      <CodeEditor
        value={content || ''}
        onChange={onContentChange || (() => {})}
        language="html"
        placeholder="<!-- Generated HTML will appear here -->"
        className="h-full"
        autoFormat={true}
      />
    </div>
  );
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const PreviewPanel: React.FC<PreviewPanelProps> = ({
  htmlContent,
  streamingContent,
  stableIframeContent,
  viewMode,
  isGenerating,
  onViewModeChange,
  onElementClick,
  onHtmlChange,
  className = ''
}) => {
  // Get current content for display
  const rawContent = streamingContent || htmlContent;
  const cleanHtmlContent = extractHtmlFromResponse(rawContent);

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between bg-white border-b border-gray-200 px-6 py-4">
        <h2 className="text-lg font-semibold text-gray-900">Preview</h2>
        <ViewModeToggle viewMode={viewMode} onViewModeChange={onViewModeChange} />
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'preview' ? (
          <PreviewIframe
            content={stableIframeContent}
            isGenerating={isGenerating}
            onElementClick={onElementClick}
          />
        ) : (
          <CodeView
            content={cleanHtmlContent}
            isGenerating={isGenerating}
            onContentChange={onHtmlChange}
          />
        )}
      </div>
    </div>
  );
};

export default PreviewPanel;
