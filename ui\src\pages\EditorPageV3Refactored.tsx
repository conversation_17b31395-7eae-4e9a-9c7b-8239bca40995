/**
 * Production-ready EditorPageV3 - Refactored with modular architecture
 * Clean, maintainable, and scalable implementation
 */

import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useEditorV3, ChatMessage } from '../hooks/useEditorV3';
import ChatInterface from '../components/Editor/ChatInterface';
import PreviewPanel from '../components/Editor/PreviewPanel';
import PageManager from '../components/Editor/PageManager';
import IntentBasedEditor from '../components/IntentBasedEditor';
import { getSession, getPageList } from '../services/pageGenService';
import { createPageWithCleanName, generatePageContentPrompt } from '../services/pageCreationService';
import type { PendingPageCreation } from '../types/pageCreation';

// ============================================================================
// TYPES
// ============================================================================

interface LocationState {
  prompt?: string;
  plan?: any;
  projectId?: number;
  sessionId?: string;
  initialGeneration?: boolean;
  loadExistingPage?: boolean;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function EditorPageV3Refactored() {
  const location = useLocation();
  const navigate = useNavigate();
  const { prompt, plan, projectId, sessionId, initialGeneration, loadExistingPage } = (location.state as LocationState) || {};

  // Use the custom hook for all editor functionality
  const { state, actions, refs } = useEditorV3({ projectId });

  // Track if initial generation has been triggered to prevent duplicates
  const [hasTriggeredInitialGeneration, setHasTriggeredInitialGeneration] = useState(false);

  // Track page loading state
  const [isLoadingPage, setIsLoadingPage] = useState(false);

  // View mode for generation preview
  const [generationViewMode, setGenerationViewMode] = useState<'preview' | 'code'>('preview');

  // Project pages state
  const [projectPages, setProjectPages] = useState<any[]>([]);
  const [isLoadingProjectPages, setIsLoadingProjectPages] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(sessionId || null);

  // New page creation state
  const [isCreatingNewPage, setIsCreatingNewPage] = useState(false);
  const [newPagePrompt, setNewPagePrompt] = useState('');
  const [showPlanReview, setShowPlanReview] = useState(false);
  const [generatedPlan, setGeneratedPlan] = useState<any>(null);
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
  const [generatedPageName, setGeneratedPageName] = useState<string>('');

  // Page management state
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const [renamingPageId, setRenamingPageId] = useState<string | null>(null);
  const [newPageName, setNewPageName] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  // Function to refresh project pages
  const refreshProjectPages = async () => {
    if (projectId) {
      setIsLoadingProjectPages(true);
      try {
        const response = await getPageList(projectId);
        if (response.sessions) {
          setProjectPages(response.sessions);
        }
      } catch (error) {
        console.error('❌ Error refreshing project pages:', error);
      } finally {
        setIsLoadingProjectPages(false);
      }
    }
  };

  // Element selector state for ChatInterface
  const [elementSelectorActive, setElementSelectorActive] = useState(false);

  // Resizable panel dimensions
  const [pagesPanelWidth, setPagesPanelWidth] = useState(280);
  const [chatPanelWidth, setChatPanelWidth] = useState(280); // Reduced chat panel for more preview space
  const [pagesPanelCollapsed, setPagesPanelCollapsed] = useState(true);

  // Resize state
  const [isResizing, setIsResizing] = useState<'pages' | 'chat' | null>(null);

  // Resize handlers
  const handleMouseDown = (type: 'pages' | 'chat') => (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(type);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return;

    if (isResizing === 'pages') {
      const newWidth = Math.max(200, Math.min(600, e.clientX));
      setPagesPanelWidth(newWidth);
    } else if (isResizing === 'chat') {
      const newWidth = Math.max(300, Math.min(600, window.innerWidth - e.clientX));
      setChatPanelWidth(newWidth);
    }
  };

  const handleMouseUp = () => {
    setIsResizing(null);
  };

  // Add global mouse event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizing]);

  // Additional state for robust UX
  const [showPageCreationDialog, setShowPageCreationDialog] = useState(false);
  const [pendingPageCreation, setPendingPageCreation] = useState<PendingPageCreation | null>(null);
  const [showLinkingDialog, setShowLinkingDialog] = useState(false);
  const [linkingProgress, setLinkingProgress] = useState<{
    current: number;
    total: number;
    currentPage: string;
  } | null>(null);
  const [isGeneratingIntent, setIsGeneratingIntent] = useState(false);

  // ============================================================================
  // INITIALIZATION EFFECTS
  // ============================================================================

  // Track if we've already shown the linking suggestion
  const [hasShownLinkingSuggestion, setHasShownLinkingSuggestion] = useState(false);

  // Simplified check for 2+ pages that need linking
  const checkIfPagesNeedLinking = (pages: any[]) => {
    const pagesWithContent = pages.filter(p => p.content && p.content.length > 50);
    // For simplicity: if we have 2+ pages, assume they need linking
    return pagesWithContent.length >= 2;
  };

  // Watch for page updates and auto-link when we have 2+ pages (with proper guards)
  useEffect(() => {
    const needsLinking = checkIfPagesNeedLinking(state.pages);

    // DISABLED: Auto-linking causes multiple popups and blinking
    // User can manually link pages using the Link Pages button
    if (false && needsLinking && !hasShownLinkingSuggestion && !showLinkingDialog && !linkingProgress) {
      console.log('🔗 Auto-linking disabled to prevent multiple popups');
      setHasShownLinkingSuggestion(true);
      handleLinkPages(); // Link immediately without dialog
    }
  }, [state.pages, hasShownLinkingSuggestion, showLinkingDialog, linkingProgress]);

  // Handle initial generation from plan page
  useEffect(() => {
    if (initialGeneration && prompt && !state.htmlContent && !hasTriggeredInitialGeneration) {
      console.log('🚀 Starting initial generation from plan page (ONCE)');
      console.log('📋 Plan data structure:', plan ? Object.keys(plan) : 'No plan');
      console.log('💬 Original prompt:', prompt);
      console.log('🔍 Plan sections:', plan?.sections?.length || 0);
      console.log('🔍 Plan features:', plan?.features?.length || 0);

      // Mark as triggered to prevent duplicates
      setHasTriggeredInitialGeneration(true);

      // Add user message
      const userMessage: ChatMessage = {
        role: 'user',
        content: prompt,
        timestamp: new Date()
      };
      actions.addMessage(userMessage);

      // Add plan message if we have a plan
      if (plan) {
        let planData;
        try {
          // Parse plan if it's a string
          planData = typeof plan === 'string' ? JSON.parse(plan) : plan;
        } catch (e) {
          planData = plan;
        }

        const planContent = formatPlanForDisplay(planData);
        const planMessage: ChatMessage = {
          role: 'assistant',
          content: planContent,
          timestamp: new Date(),
          type: 'plan'
        };
        actions.addMessage(planMessage);
      }

      // Create comprehensive prompt using BOTH original prompt AND plan data
      let comprehensivePrompt = prompt;

      if (plan && typeof plan === 'object') {
        // Build detailed prompt from plan data
        comprehensivePrompt = `${prompt}

📋 **DETAILED IMPLEMENTATION PLAN:**

🎯 **Project Overview:**
${plan.overview || 'Create a professional, modern design'}

🏗️ **Implementation Requirements:**`;

        // Add each section from the plan
        if (plan.sections && Array.isArray(plan.sections)) {
          plan.sections.forEach((section: any, index: number) => {
            comprehensivePrompt += `

${index + 1}. **${section.title}**
   ${section.description}`;

            if (section.details && Array.isArray(section.details)) {
              section.details.forEach((detail: string) => {
                comprehensivePrompt += `
   • ${detail}`;
              });
            }
          });
        }

        // Add features
        if (plan.features && Array.isArray(plan.features)) {
          comprehensivePrompt += `

✨ **Key Features to Implement:**`;
          plan.features.forEach((feature: string) => {
            comprehensivePrompt += `
• ${feature}`;
          });
        }

        // Add accessibility requirements
        if (plan.accessibility && Array.isArray(plan.accessibility)) {
          comprehensivePrompt += `

♿ **Accessibility Requirements:**`;
          plan.accessibility.forEach((item: string) => {
            comprehensivePrompt += `
• ${item}`;
          });
        }
      }

      // Add modal-ready instructions
      comprehensivePrompt += `

⚡ **INTERACTIVE ELEMENTS:**
- Create buttons and forms that can be enhanced with modal functionality
- Any unimplemented interactive elements will show ⚡ indicators
- Ensure professional design ready for modal overlays
- Include proper structure for future enhancements`;

      console.log('🎯 Using comprehensive prompt with plan data:', comprehensivePrompt.substring(0, 200) + '...');
      console.log('📏 Final prompt length:', comprehensivePrompt.length);
      console.log('🔍 Plan sections included:', plan?.sections?.length || 0);
      console.log('🔍 Plan features included:', plan?.features?.length || 0);

      // Start generation with comprehensive prompt
      actions.generateFromPrompt(comprehensivePrompt);
    }
  }, [initialGeneration, prompt, state.htmlContent, plan, hasTriggeredInitialGeneration]);

  // Listen for navigation messages from iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'NAVIGATE_TO_PAGE') {
        const { pageId, pageName } = event.data;
        console.log('🔗 Navigation message received:', { pageId, pageName });

        // Debug: Check if this is a known page name
        const existingPageNames = state.pages.map(p => p.name);
        console.log('🔗 Existing pages:', existingPageNames);
        console.log('🔗 Clicked page name:', pageName);

        // Handle navigation click
        handleNavigationClick({
          textContent: pageName,
          isNavigation: true
        });
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [state.pages]);

  // Load existing page content when sessionId is provided
  useEffect(() => {
    if (loadExistingPage && sessionId && !state.htmlContent && !hasTriggeredInitialGeneration) {
      console.log('📄 Loading existing page content for session:', sessionId);

      const loadPageContent = async () => {
        setIsLoadingPage(true);

        try {
          const response = await getSession(sessionId);
          if (response.success && response.session) {
            console.log('✅ Page content loaded successfully');
            console.log('🔧 Page HTML length:', response.session.page_html?.length);
            console.log('🔧 Page HTML starts with:', response.session.page_html?.substring(0, 100));
            console.log('🔧 Page HTML includes userDashboard:', response.session.page_html?.includes('userDashboard'));

            // Set the HTML content in the editor
            actions.setHtmlContent(response.session.page_html);
            actions.setStableIframeContent(response.session.page_html);

            console.log('🔧 Content set in state, checking state values...');
            console.log('🔧 State htmlContent length:', state.htmlContent?.length);
            console.log('🔧 State stableIframeContent length:', state.stableIframeContent?.length);

            // Add a welcome message
            actions.addMessage({
              role: 'assistant',
              content: `📄 Loaded existing page. You can now edit this page or ask me to make changes.`,
              timestamp: new Date()
            });

            // Mark as triggered to prevent other loading attempts
            setHasTriggeredInitialGeneration(true);

            // Update current session ID for UI highlighting
            setCurrentSessionId(sessionId);
          }
        } catch (error) {
          console.error('❌ Error loading page content:', error);
          actions.addMessage({
            role: 'assistant',
            content: `❌ Failed to load page content. Please try again or create a new page.`,
            timestamp: new Date()
          });
        } finally {
          setIsLoadingPage(false);
        }
      };

      loadPageContent();
    }
  }, [loadExistingPage, sessionId, state.htmlContent, hasTriggeredInitialGeneration, actions]);

  // Load project pages when component mounts or projectId changes
  useEffect(() => {
    if (projectId) {
      console.log('📄 Loading project pages for project:', projectId);

      const loadProjectPages = async () => {
        setIsLoadingProjectPages(true);
        try {
          const response = await getPageList(projectId);
          if (response.sessions) {
            console.log('✅ Project pages loaded:', response.sessions.length);
            setProjectPages(response.sessions);

            // Auto-expand pages panel when project pages are loaded
            if (response.sessions.length > 0) {
              setPagesPanelCollapsed(false);
            } else {
              // If no pages exist, automatically show the create new page UI
              console.log('📄 No pages found, showing create new page UI');
              setIsCreatingNewPage(true);
              setShowPlanReview(false);
              setNewPagePrompt('');
              setGeneratedPageName('');
            }
          }
        } catch (error) {
          console.error('❌ Error loading project pages:', error);
        } finally {
          setIsLoadingProjectPages(false);
        }
      };

      loadProjectPages();
    }
  }, [projectId]);

  // REMOVED: Periodic auto-refresh - users can manually refresh when needed

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================



  const handleChatSubmit = async (message: string) => {
    // Add user message
    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    };
    actions.addMessage(userMessage);
    actions.clearInput();

    // Determine if this is an edit or new generation
    const hasContent = state.htmlContent.length > 0 || state.stableIframeContent.length > 0;

    if (hasContent) {
      // Always use the useEditorV3 editContent for chat messages
      // This will update state.htmlContent which will flow to IntentBasedEditor
      await actions.editContent(message);
    } else {
      await actions.generateFromPrompt(message);
    }
  };

  // Step 1: Generate Intent like Readdy.ai does
  const generateIntentFromElement = async (element: any) => {
    // DISABLED: Using new IntentBasedEditor component instead
    console.log('🔥 Old intent generation disabled - using new IntentBasedEditor system');
    return;

    // OLD CODE DISABLED TO PREVENT DUPLICATE CALLS
    // Prevent multiple intent calls for the same element
    if (element.intentGenerating || element.intentData || isGeneratingIntent) {
      console.log('🔥 Intent already generating or exists for this element');
      return;
    }

    // Mark this element as being processed
    element.intentGenerating = true;
    setIsGeneratingIntent(true);

    console.log('🔥 Starting intent generation for element:', element.textContent);
    const tagName = element.tagName?.toLowerCase() || 'div';
    const elementCode = element.outerHTML || `<${tagName}>${element.textContent || ''}</${tagName}>`;

    try {
      // Step 1: Call intent generation API (like Readdy's /api/page_gen/generate_intent)
      const response = await fetch('/api/llm/v3/generate-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          elementCode,
          htmlContent: state.htmlContent || state.stableIframeContent,
          conversationHistory: state.messages
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 'OK' && result.data) {
          // Display the intent and suggestion like Readdy does
          actions.addMessage({
            role: 'assistant',
            content: result.data.userIntent,
            timestamp: new Date()
          });

          if (result.data.suggestion) {
            actions.addMessage({
              role: 'assistant',
              content: result.data.suggestion,
              timestamp: new Date()
            });
          }

          // Store the intent for later use in implementation
          element.intentData = result.data;
        }
      }
    } catch (error) {
      console.error('Error generating intent:', error);
      // Fallback message
      actions.addMessage({
        role: 'assistant',
        content: `I understand you want to implement functionality for "${element.textContent}". Let me help you with that.`,
        timestamp: new Date()
      });
    } finally {
      element.intentGenerating = false;
      setIsGeneratingIntent(false);
    }
  };

  // Debounce element clicks to prevent rapid-fire calls
  const [lastClickTime, setLastClickTime] = useState(0);
  const CLICK_DEBOUNCE_MS = 1000; // 1 second debounce

  const handleElementClick = async (element: any) => {
    // DISABLED: Using new IntentBasedEditor component for element interactions
    console.log('🔥 Old element click handler disabled - using new IntentBasedEditor system');
    return;

    // OLD CODE DISABLED TO PREVENT CONFLICTS WITH NEW SYSTEM
    const now = Date.now();

    // Debounce rapid clicks on the same element
    if (now - lastClickTime < CLICK_DEBOUNCE_MS) {
      console.log('🔥 Click debounced, ignoring rapid click');
      return;
    }

    setLastClickTime(now);
    console.log('🔥 Element clicked in iframe:', element);

    // Handle navigation clicks
    if (element.isNavigation) {
      console.log('🔥 Navigation detected, handling navigation click');
      handleNavigationClick(element);
    } else if ((element.implementationType && element.implementationReason) || element.isInteractive) {
      // Handle interactive elements that need implementation
      console.log('🔥 Interactive element needs implementation:', element.implementationReason);

      // Step 1: Generate intent like Readdy does (only if not already generated)
      if (!element.intentData) {
        await generateIntentFromElement(element);
      }

      actions.setSelectedElement(element);
      actions.setShowImplementModal(true);
    } else {
      console.log('🔥 Element does not need implementation, ignoring');
    }
  };

  const handleNavigationClick = async (element: any) => {
    const rawPageName = element.textContent.trim();

    try {
      // Create page with clean name using the new service
      const result = await createPageWithCleanName(
        { prompt: rawPageName },
        'navigation-link'
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to create page');
      }

      const { pageName, pageId } = result;
      console.log('🔥 Processing navigation click:', { rawPageName, pageName, pageId });

      // Smart page matching: handle both "Reports" and "Reports Page" scenarios
      const existingPage = state.pages.find(p => {
        const normalizedPageName = pageName!.toLowerCase().trim();
        const normalizedExistingName = p.name.toLowerCase().trim();

        // Direct matches
        if (p.id === pageId || normalizedExistingName === normalizedPageName) {
          return true;
        }

        // Handle "Reports" → "Reports Page" mismatch
        if (normalizedExistingName === normalizedPageName + ' page') {
          return true;
        }

        // Handle "Reports Page" → "Reports" mismatch
        if (normalizedPageName === normalizedExistingName + ' page') {
          return true;
        }

        // Handle page ID matches (e.g., "reports" matches "reports-page")
        const clickedPageId = pageId;
        const existingPageId = p.id;
        const clickedPageIdWithPage = `${pageId}-page`;

        if (existingPageId === clickedPageId || existingPageId === clickedPageIdWithPage || clickedPageId === existingPageId) {
          return true;
        }

        return false;
      });

      if (existingPage) {
        console.log('🔥 Page already exists, switching to:', existingPage);
        actions.switchToPage(existingPage.id);

        // Add feedback message
        actions.addMessage({
          role: 'assistant',
          content: `✅ Switched to "${existingPage.name}" page`,
          timestamp: new Date()
        });
      } else {
        console.log('🔥 Page does not exist, showing creation dialog');

        // Show confirmation dialog instead of creating immediately
        const pendingCreation: PendingPageCreation = {
          pageName: pageName!,
          pageId: pageId!,
          source: 'navigation-link',
          originalPrompt: rawPageName
        };
        setPendingPageCreation(pendingCreation);
        setShowPageCreationDialog(true);
      }
    } catch (error) {
      console.error('❌ Error processing navigation click:', error);

      // Fallback to basic page creation
      const pageName = rawPageName;
      const pageId = pageName.toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-');
      const pendingCreation: PendingPageCreation = {
        pageName,
        pageId,
        source: 'navigation-link',
        originalPrompt: rawPageName
      };
      setPendingPageCreation(pendingCreation);
      setShowPageCreationDialog(true);
    }
  };

  const confirmPageCreation = async () => {
    if (!pendingPageCreation) {
      console.log('❌ No pending page creation');
      return;
    }

    const { pageName, pageId, source, originalPrompt } = pendingPageCreation;
    console.log('🚀 Confirming page creation:', { pageName, pageId, source });

    // Close dialog
    setShowPageCreationDialog(false);
    setPendingPageCreation(null);

    try {
      // Clear current page selection to prepare for new page creation
      setCurrentSessionId(null);
      actions.setHtmlContent('');
      actions.setStableIframeContent('');

      // Add feedback message
      actions.addMessage({
        role: 'assistant',
        content: `🚀 Creating new "${pageName}" page...`,
        timestamp: new Date()
      });

      // Generate content using the same pattern as main page creation
      // This will auto-save the page after HTML generation completes
      const prompt = generatePageContentPrompt(pageName, state.pages);
      console.log('🎯 Generating content with prompt:', prompt.substring(0, 100) + '...');

      try {
        await actions.generateFromPrompt(prompt, pageName);
        console.log('✅ Page generation completed');

        // After generation completes, refresh pages and switch to the new page
        setTimeout(async () => {
          await refreshProjectPages();

          // Find and switch to the newly created page
          const updatedPages = await getPageList(projectId!);
          if (updatedPages.sessions && updatedPages.sessions.length > 0) {
            // Find the page by name (since we know the pageName)
            const newPage = updatedPages.sessions.find((p: any) =>
              p.pageUrl === pageName.toLowerCase().replace(/\s+/g, '-') ||
              (p.pageUrl && p.pageUrl.includes(pageName.toLowerCase().replace(/\s+/g, '-'))) ||
              p.title === pageName
            );

            if (newPage) {
              console.log('🎯 Switching to newly created page:', newPage);
              setCurrentSessionId(newPage.id.toString());

              // Load the new page content
              const response = await getSession(newPage.id.toString());
              if (response.success && response.session) {
                actions.setHtmlContent(response.session.page_html);
                actions.setStableIframeContent(response.session.page_html);

                // Update active flags: check if page exists in state, if not add it
                const existingPageInState = state.pages.find(p => p.id === newPage.id.toString());
                if (existingPageInState) {
                  // Page exists, just switch to it (this will set active flags properly)
                  actions.switchToPage(newPage.id.toString());
                } else {
                  // Page doesn't exist in state, add it with active flag
                  const pageForState = {
                    id: newPage.id.toString(),
                    name: newPage.title || pageName,
                    content: response.session.page_html,
                    isActive: true
                  };
                  actions.addPage(pageForState);
                }

                actions.addMessage({
                  role: 'assistant',
                  content: `✅ Successfully created and switched to "${pageName}" page!`,
                  timestamp: new Date()
                });
              }
            }
          }
        }, 2000); // Wait a bit for auto-save to complete

        console.log('🔗 Page created successfully. Use Link Pages button to add navigation.');

      } catch (error) {
        console.error('❌ Page generation failed:', error);
        actions.addMessage({
          role: 'assistant',
          content: `❌ Failed to generate content for "${pageName}". Please try again.`,
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error('❌ Error creating page:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to create page. Please try again.`,
        timestamp: new Date()
      });
    }
  };

  const cancelPageCreation = () => {
    setShowPageCreationDialog(false);
    setPendingPageCreation(null);

    actions.addMessage({
      role: 'assistant',
      content: `❌ Page creation cancelled`,
      timestamp: new Date()
    });
  };

  const handlePageSwitch = (pageId: string) => {
    actions.switchToPage(pageId);

    const page = state.pages.find(p => p.id === pageId);
    if (page) {
      actions.addMessage({
        role: 'assistant',
        content: `📄 Switched to "${page.name}" page`,
        timestamp: new Date()
      });
    }
  };

  // Handle project page selection (load page content directly)
  const handleProjectPageSelect = async (page: any) => {
    console.log('📄 Project page selected:', page);

    // Don't reload if it's the same page
    if (currentSessionId === page.id) {
      console.log('📄 Same page selected, ignoring');
      return;
    }

    // Hide new page creation UI when selecting an existing page
    setIsCreatingNewPage(false);
    setNewPagePrompt('');
    setShowPlanReview(false);
    setGeneratedPageName('');
    setGeneratedPlan(null);

    // Set loading state
    setIsLoadingPage(true);

    try {
      // Load the page content
      const response = await getSession(page.id);
      if (response.success && response.session) {
        console.log('✅ Page content loaded successfully');

        // Set the HTML content in the editor
        actions.setHtmlContent(response.session.page_html);
        actions.setStableIframeContent(response.session.page_html);

        // Update current session ID for UI highlighting
        setCurrentSessionId(page.id);

        // Update active flags: set this page as active, others as inactive
        // First, find if this page exists in the state.pages array
        const existingPage = state.pages.find(p => p.id === page.id);
        if (existingPage) {
          // Use switchToPage to properly manage active flags
          actions.switchToPage(page.id);
          // Note: currentSessionId is already set above, switchToPage handles the state.pages active flags
        } else {
          // If page doesn't exist in state.pages, add it and set as active
          const newPage = {
            id: page.id,
            name: page.title || `Page ${page.id}`,
            content: response.session.page_html,
            isActive: true
          };
          actions.addPage(newPage);
          // Note: currentSessionId is already set above, addPage handles the state.pages active flags
        }

        // Add a message to show page switch
        actions.addMessage({
          role: 'assistant',
          content: `📄 Switched to "${page.title || `Page ${page.id}`}". You can now edit this page or ask me to make changes.`,
          timestamp: new Date()
        });

        console.log('✅ Page switched successfully');
      }
    } catch (error) {
      console.error('❌ Error loading page content:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to load page content. Please try again.`,
        timestamp: new Date()
      });
    } finally {
      setIsLoadingPage(false);
    }
  };

  const handlePageAdd = (page: any) => {
    actions.addPage(page);
    actions.switchToPage(page.id);

    actions.addMessage({
      role: 'assistant',
      content: `📄 Created new page "${page.name}". Describe what content you'd like on this page.`,
      timestamp: new Date()
    });
  };

  // Handle new page creation button click
  const handleCreateNewPage = () => {
    // Clear current page selection
    setCurrentSessionId(null);

    // Clear editor content
    actions.setHtmlContent('');
    actions.setStableIframeContent('');

    // Clear all active flags from existing pages
    state.pages.forEach(page => {
      if (page.isActive) {
        actions.updatePage(page.id, { isActive: false });
      }
    });

    // Note: currentSessionId is already cleared above (setCurrentSessionId(null))

    // Show new page creation mode
    setIsCreatingNewPage(true);
    setNewPagePrompt('');

    // Add welcome message
    actions.addMessage({
      role: 'assistant',
      content: `🚀 Ready to create a new page! Describe what kind of page you'd like to create and I'll generate it for you.`,
      timestamp: new Date()
    });
  };

  // Handle new page prompt submission - Generate plan first
  const handleNewPagePromptSubmit = async () => {
    if (!newPagePrompt.trim() || !projectId) return;

    setIsGeneratingPlan(true);

    try {
      // Use the already generated page name
      const pageName = generatedPageName || 'New Page';
      console.log('📄 Using generated page name:', pageName, 'from prompt:', newPagePrompt);

      // Add user message
      actions.addMessage({
        role: 'user',
        content: newPagePrompt,
        timestamp: new Date()
      });

      // Add assistant message about plan generation
      actions.addMessage({
        role: 'assistant',
        content: `🎯 Generating plan for "${pageName}" page...`,
        timestamp: new Date()
      });

      // Generate structured plan using the existing API
      const response = await fetch('/api/llm/v3/plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          prompt: newPagePrompt,
          deviceType: 'desktop' // Default to desktop for pages
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.plan) {
          console.log('✅ Plan generated successfully:', result.plan);

          // Store the plan and show review
          setGeneratedPlan(result.plan);
          setShowPlanReview(true);

          // Add plan to chat
          actions.addMessage({
            role: 'assistant',
            content: `📋 Here's the plan for your "${pageName}" page. Review it and click "Generate Page" to proceed:

${formatPlanForDisplay(result.plan)}`,
            timestamp: new Date()
          });
        } else {
          throw new Error('No plan returned from API');
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Plan generation API failed');
      }

    } catch (error) {
      console.error('❌ Error generating plan:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to generate plan. Please try again.`,
        timestamp: new Date()
      });
    } finally {
      setIsGeneratingPlan(false);
    }
  };

  // Handle plan approval and page generation
  const handleGeneratePageFromPlan = async () => {
    if (!generatedPlan || !newPagePrompt.trim()) return;

    try {
      const pageName = generatedPageName || 'New Page';

      // Immediately switch to code/preview mode by resetting plan review state
      setIsCreatingNewPage(false);
      setShowPlanReview(false);

      // Add assistant message about page creation
      actions.addMessage({
        role: 'assistant',
        content: `🚀 Creating "${pageName}" page based on the approved plan...`,
        timestamp: new Date()
      });

      // Start generation with project context and clean page name
      await actions.generateFromPrompt(newPagePrompt, pageName);

      // Reset remaining new page creation state
      setNewPagePrompt('');
      setGeneratedPlan(null);

      // After generation completes, refresh pages and switch to the new page
      setTimeout(async () => {
        await refreshProjectPages();

        // Find and switch to the newly created page
        const updatedPages = await getPageList(projectId!);
        if (updatedPages.sessions && updatedPages.sessions.length > 0) {
          // Find the page by name (since we know the pageName)
          const newPage = updatedPages.sessions.find((p: any) =>
            p.pageUrl === pageName.toLowerCase().replace(/\s+/g, '-') ||
            (p.pageUrl && p.pageUrl.includes(pageName.toLowerCase().replace(/\s+/g, '-'))) ||
            p.title === pageName
          );

          if (newPage) {
            console.log('🎯 Switching to newly created page:', newPage);
            setCurrentSessionId(newPage.id.toString());

            // Load the new page content
            const response = await getSession(newPage.id.toString());
            if (response.success && response.session) {
              actions.setHtmlContent(response.session.page_html);
              actions.setStableIframeContent(response.session.page_html);

              // Update active flags: check if page exists in state, if not add it
              const existingPageInState = state.pages.find(p => p.id === newPage.id.toString());
              if (existingPageInState) {
                // Page exists, just switch to it (this will set active flags properly)
                actions.switchToPage(newPage.id.toString());
                // currentSessionId is already set above
              } else {
                // Page doesn't exist in state, add it with active flag
                const pageForState = {
                  id: newPage.id.toString(),
                  name: newPage.title || pageName,
                  content: response.session.page_html,
                  isActive: true
                };
                actions.addPage(pageForState);
                // currentSessionId is already set above
              }

              actions.addMessage({
                role: 'assistant',
                content: `✅ Successfully created and switched to "${pageName}" page!`,
                timestamp: new Date()
              });
            }
          }
        }
      }, 2000); // Wait a bit for auto-save to complete

    } catch (error) {
      console.error('❌ Error creating page:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to create page. Please try again.`,
        timestamp: new Date()
      });
    }
  };

  // Handle plan rejection - go back to prompt editing
  const handleRejectPlan = () => {
    setShowPlanReview(false);
    setGeneratedPlan(null);

    actions.addMessage({
      role: 'assistant',
      content: `📝 Plan rejected. You can modify your prompt and try again.`,
      timestamp: new Date()
    });
  };

  // Cancel new page creation
  const handleCancelNewPage = () => {
    // Only set isCreatingNewPage to false if there are existing pages
    // If no pages exist, keep the prompt visible
    if (projectPages.length > 0) {
      setIsCreatingNewPage(false);
    }
    setNewPagePrompt('');
    setShowPlanReview(false);
    setGeneratedPageName('');

    actions.addMessage({
      role: 'assistant',
      content: `❌ New page creation cancelled.`,
      timestamp: new Date()
    });
  };

  // Handle page rename
  const handlePageRename = async (pageId: string, newName: string) => {
    if (!newName.trim()) return;

    try {
      // Call API to rename page
      const response = await fetch(`/api/page_gen/session/rename`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          sessionId: pageId,
          newTitle: newName.trim()
        })
      });

      if (response.ok) {
        // Refresh the pages list to show updated name
        await refreshProjectPages();

        actions.addMessage({
          role: 'assistant',
          content: `✅ Page renamed to "${newName.trim()}"`,
          timestamp: new Date()
        });
      } else {
        throw new Error('Failed to rename page');
      }
    } catch (error) {
      console.error('Error renaming page:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to rename page. Please try again.`,
        timestamp: new Date()
      });
    } finally {
      setRenamingPageId(null);
      setNewPageName('');
    }
  };

  // Handle page delete
  const handlePageDelete = async (pageId: string) => {
    try {
      // Call API to delete page
      const response = await fetch(`/api/page_gen/session/delete`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          sessionId: pageId
        })
      });

      if (response.ok) {
        // If we're deleting the current page, clear the editor
        if (currentSessionId === pageId) {
          setCurrentSessionId(null);
          actions.setHtmlContent('');
          actions.setStableIframeContent('');

          // Clear current page ID in state to prevent infinite loops
          if (state.currentPageId === pageId) {
            // Reset to empty state instead of null
            actions.setHtmlContent('');
            actions.setStableIframeContent('');
          }
        }

        // Refresh the pages list
        await refreshProjectPages();

        // Check if this was the last page - if so, show create new page UI
        const updatedPages = projectPages.filter(p => p.id !== pageId);
        if (updatedPages.length === 0) {
          console.log('📄 Last page deleted, switching to create new page mode');
          setIsCreatingNewPage(true);
          setShowPlanReview(false);
          setNewPagePrompt('');
          setGeneratedPageName('');
        }

        actions.addMessage({
          role: 'assistant',
          content: `✅ Page deleted successfully`,
          timestamp: new Date()
        });
      } else {
        throw new Error('Failed to delete page');
      }
    } catch (error) {
      console.error('Error deleting page:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to delete page. Please try again.`,
        timestamp: new Date()
      });
    } finally {
      setShowDeleteConfirm(null);
    }
  };

  // Start rename process
  const startRename = (page: any) => {
    console.log('🔄 startRename called with page:', page);
    setRenamingPageId(page.id);
    setNewPageName(page.title || `Page ${page.id}`);
    setOpenDropdownId(null);
  };

  // Cancel rename
  const cancelRename = () => {
    setRenamingPageId(null);
    setNewPageName('');
  };

  // Confirm rename
  const confirmRename = (pageId: string) => {
    handlePageRename(pageId, newPageName);
  };

  // Start delete process
  const startDelete = (pageId: string) => {
    console.log('🗑️ startDelete called with pageId:', pageId);
    setShowDeleteConfirm(pageId);
    setOpenDropdownId(null);
  };

  // Cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirm(null);
  };

  // Confirm delete
  const confirmDelete = (pageId: string) => {
    handlePageDelete(pageId);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdownId && event.target) {
        // Check if the click is outside the dropdown menu
        const target = event.target as Element;
        const dropdown = target.closest('.dropdown-menu');
        const dropdownButton = target.closest('.dropdown-button');

        // Don't close if clicking inside dropdown or on dropdown button
        if (!dropdown && !dropdownButton) {
          console.log('🔄 Closing dropdown due to outside click');
          setOpenDropdownId(null);
        }
      }
    };

    if (openDropdownId) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [openDropdownId]);

  // Generate page name when prompt changes using the new service
  useEffect(() => {
    if (newPagePrompt.trim()) {
      // Add a small delay to debounce rapid changes
      const timeoutId = setTimeout(async () => {
        try {
          const result = await createPageWithCleanName(
            { prompt: newPagePrompt },
            'new-page-flow'
          );

          if (result.success && result.pageName) {
            setGeneratedPageName(result.pageName);
          } else {
            console.warn('Page name generation failed:', result.error);
            setGeneratedPageName('New Page'); // Fallback
          }
        } catch (error) {
          console.error('❌ Error generating page name:', error);
          setGeneratedPageName('New Page'); // Fallback
        }
      }, 300); // 300ms debounce

      return () => clearTimeout(timeoutId);
    } else {
      setGeneratedPageName('');
    }
  }, [newPagePrompt]);

  const handleLinkPages = async () => {
    const pagesWithContent = state.pages.filter(p => p.content && p.content.length > 0);

    if (pagesWithContent.length < 2) {
      actions.addMessage({
        role: 'assistant',
        content: '⚠️ Need at least 2 pages with content to link navigation',
        timestamp: new Date()
      });
      return;
    }

    // Prevent multiple linking operations
    if (linkingProgress) {
      console.log('🔗 Linking already in progress, ignoring duplicate call');
      return;
    }

    // Close linking dialog if open
    setShowLinkingDialog(false);

    // Initialize progress tracking
    setLinkingProgress({
      current: 0,
      total: pagesWithContent.length,
      currentPage: 'Starting...'
    });

    actions.addMessage({
      role: 'assistant',
      content: `🔗 Linking ${pagesWithContent.length} pages with navigation...`,
      timestamp: new Date()
    });

    try {
      // Use the improved linking with progress callback
      await linkPagesWithProgress(pagesWithContent);

      actions.addMessage({
        role: 'assistant',
        content: '✅ All pages have been linked with navigation!',
        timestamp: new Date()
      });

      // Reset linking suggestion state so it can show again for new pages
      setHasShownLinkingSuggestion(false);
    } catch (error) {
      console.error('Linking failed:', error);
      actions.addMessage({
        role: 'assistant',
        content: '❌ Failed to link some pages. Please try again.',
        timestamp: new Date()
      });
    } finally {
      setLinkingProgress(null);
    }
  };

  // CLIENT-SIDE LINKING: Fast, immediate, no API calls needed
  const linkPagesWithProgress = async (pages: any[]) => {
    console.log(`🔗 Starting CLIENT-SIDE linking for ${pages.length} pages`);

    // Process all pages immediately on client-side
    pages.forEach((page, index) => {
      const otherPageNames = pages
        .filter(p => p.id !== page.id)
        .map(p => p.name);

      console.log(`🔗 Client-side linking page ${index + 1}/${pages.length}: ${page.name}`);

      // Update progress for this page
      setLinkingProgress({
        current: index + 1,
        total: pages.length,
        currentPage: page.name
      });

      try {
        // Parse the HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(page.content, 'text/html');

        // Find navigation area (try multiple selectors)
        let navElement = doc.querySelector('nav') ||
                        doc.querySelector('.nav') ||
                        doc.querySelector('.navigation') ||
                        doc.querySelector('header nav') ||
                        doc.querySelector('.header nav');

        if (!navElement) {
          // If no nav found, try to find header and add nav there
          const header = doc.querySelector('header') || doc.querySelector('.header');
          if (header) {
            navElement = doc.createElement('nav');
            navElement.className = 'navigation-links';
            header.appendChild(navElement);
          } else {
            // Create a simple nav at the top of body
            navElement = doc.createElement('nav');
            navElement.className = 'navigation-links';
            navElement.style.cssText = 'padding: 1rem; background: #f8f9fa; border-bottom: 1px solid #dee2e6;';
            doc.body.insertBefore(navElement, doc.body.firstChild);
          }
        }

        // Clear existing auto-generated navigation links (but keep original nav content)
        const existingAutoLinks = navElement.querySelectorAll('a[data-page-link="true"]');
        existingAutoLinks.forEach(link => link.remove());

        // Also remove any links that match other page names (to prevent duplicates)
        const allLinks = navElement.querySelectorAll('a');
        allLinks.forEach(link => {
          const linkText = link.textContent?.trim().toLowerCase();
          const isPageLink = otherPageNames.some(pageName => {
            const normalizedPageName = pageName.toLowerCase();
            return linkText === normalizedPageName ||
                   linkText === (normalizedPageName + ' page') ||
                   linkText === normalizedPageName.replace(' page', '') ||
                   (linkText + ' page') === normalizedPageName;
          });
          if (isPageLink) {
            link.remove();
          }
        });

        // Add links to other pages with proper navigation attributes
        otherPageNames.forEach(pageName => {
          const link = doc.createElement('a');
          link.href = '#';

          // Use the clean page name for display (remove " Page" suffix if present)
          const displayName = pageName.endsWith(' Page') ? pageName.replace(' Page', '') : pageName;
          link.textContent = displayName;

          // Use data-nav attribute for proper navigation (this is what the system expects)
          const pageId = pageName.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '-')
            .replace(/^-+|-+$/g, '');

          link.setAttribute('data-nav', pageId);
          link.setAttribute('data-page-link', 'true'); // For our tracking
          link.style.cssText = 'margin-right: 1rem; color: #007bff; text-decoration: none; cursor: pointer;';

          navElement.appendChild(link);
        });

        // Ensure the page has proper SPA router script for navigation
        let scriptElement = doc.querySelector('script[data-exec="inline"]');
        if (!scriptElement) {
          scriptElement = doc.createElement('script');
          scriptElement.setAttribute('data-exec', 'inline');
          scriptElement.textContent = `
// SPA Router for page navigation
document.addEventListener('click', (e) => {
  const target = e.target.closest('[data-nav]');
  if (target) {
    e.preventDefault();
    const pageId = target.getAttribute('data-nav');
    const pageName = target.textContent.trim();

    console.log('🔗 Navigation click in iframe:', {
      pageId,
      pageName,
      targetElement: target.outerHTML
    });

    // Send message to parent to switch pages
    if (window.parent && window.parent.postMessage) {
      window.parent.postMessage({
        type: 'NAVIGATE_TO_PAGE',
        pageId: pageId,
        pageName: pageName
      }, '*');
    }
  }
});
          `;
          doc.body.appendChild(scriptElement);
        }

        // Update the page content with modified HTML
        const updatedHtml = doc.documentElement.outerHTML;
        actions.updatePage(page.id, { content: updatedHtml });
        console.log(`✅ Client-side linking completed for ${page.name}`);

      } catch (error) {
        console.error(`❌ Failed to update page ${page.name}:`, error);
      }
    });

    // No need to wait for API calls - everything is done immediately!
    console.log('🔗 All pages linked instantly via client-side manipulation');
  };

  const extractHtmlFromResponse = (response: string): string => {
    if (!response) return '';

    let htmlFragment = '';

    // First, check if this is already a complete HTML document
    if (response.trim().startsWith('<!DOCTYPE html') || response.trim().startsWith('<html')) {
      return response.trim();
    }

    // Look for HTML content between ```html and ``` markers
    const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
    if (htmlMatch) {
      htmlFragment = htmlMatch[1].trim();
    } else if (response.includes('<') && response.includes('>')) {
      // If response contains HTML tags, assume it's HTML fragment
      const firstTagIndex = response.indexOf('<');
      htmlFragment = response.substring(firstTagIndex).trim();
    } else {
      return response;
    }

    // If it's a fragment (doesn't start with DOCTYPE or html), wrap it in parent structure
    if (htmlFragment && !htmlFragment.startsWith('<!DOCTYPE') && !htmlFragment.startsWith('<html')) {
      return createParentHtmlStructure(htmlFragment);
    }

    return htmlFragment;
  };

  // Create parent HTML structure with Tailwind CSS for fragments
  const createParentHtmlStructure = (fragment: string): string => {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Event delegation for data-action attributes
    document.addEventListener('DOMContentLoaded', function() {
      document.addEventListener('click', function(e) {
        const target = e.target.closest('[data-action]');
        if (target) {
          const action = target.getAttribute('data-action');
          const targetId = target.getAttribute('data-target');

          switch(action) {
            case 'openModal':
              if (targetId) openModal(targetId);
              break;
            case 'closeModal':
              if (targetId) closeModal(targetId);
              break;
            default:
              console.log('Action triggered:', action, 'Target:', targetId);
          }
        }
      });
    });

    // Modal functions
    function openModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
      }
    }

    function closeModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
      }
    }
  </script>
</head>
<body>
  <div id="app">
    ${fragment}
  </div>
</body>
</html>`;
  };

  const handleImplementChoice = async (choice: 'inline' | 'modal' | 'page') => {
    if (!state.selectedElement) return;

    // Prevent multiple implementation calls
    if (state.isGenerating) {
      console.log('🔥 Implementation already in progress, ignoring');
      return;
    }

    actions.setShowImplementModal(false);

    const elementText = state.selectedElement.textContent;
    const elementType = state.selectedElement.implementationType || 'interactive';

    // Add user message showing their choice
    actions.addMessage({
      role: 'user',
      content: `Implement "${elementText}" as ${choice === 'inline' ? 'inline functionality' : choice === 'modal' ? 'a modal/popup' : 'a new page'}`,
      timestamp: new Date()
    });

    if (choice === 'page') {
      // Handle page creation aligned with main page creation workflow
      try {
        const result = await createPageWithCleanName(
          { prompt: elementText },
          'element-implementation'
        );

        if (!result.success) {
          throw new Error(result.error || 'Failed to create page');
        }

        const { pageName, pageId } = result;

        // Check if page already exists
        const existingPage = state.pages.find(p => p.id === pageId);
        if (existingPage) {
          actions.switchToPage(pageId!);
          actions.addMessage({
            role: 'assistant',
            content: `✅ Switched to existing "${pageName}" page`,
            timestamp: new Date()
          });
          return;
        }

        // Clear current page selection to prepare for new page creation
        setCurrentSessionId(null);
        actions.setHtmlContent('');
        actions.setStableIframeContent('');

        // Add assistant message about page creation
        actions.addMessage({
          role: 'assistant',
          content: `🚀 Creating "${pageName}" page for this feature...`,
          timestamp: new Date()
        });

        // Generate content using the same pattern as main page creation
        // This will auto-save the page after HTML generation completes
        const prompt = generatePageContentPrompt(pageName!, state.pages);
        await actions.generateFromPrompt(prompt, pageName!);

        // After generation completes, refresh pages and switch to the new page
        setTimeout(async () => {
          await refreshProjectPages();

          // Find and switch to the newly created page
          const updatedPages = await getPageList(projectId!);
          if (updatedPages.sessions && updatedPages.sessions.length > 0) {
            // Find the page by name (since we know the pageName)
            const newPage = updatedPages.sessions.find((p: any) =>
              p.pageUrl === pageName!.toLowerCase().replace(/\s+/g, '-') ||
              (p.pageUrl && p.pageUrl.includes(pageName!.toLowerCase().replace(/\s+/g, '-'))) ||
              p.title === pageName
            );

            if (newPage) {
              console.log('🎯 Switching to newly created page:', newPage);
              setCurrentSessionId(newPage.id.toString());

              // Load the new page content
              const response = await getSession(newPage.id.toString());
              if (response.success && response.session) {
                actions.setHtmlContent(response.session.page_html);
                actions.setStableIframeContent(response.session.page_html);

                // Update active flags: check if page exists in state, if not add it
                const existingPageInState = state.pages.find(p => p.id === newPage.id.toString());
                if (existingPageInState) {
                  // Page exists, just switch to it (this will set active flags properly)
                  actions.switchToPage(newPage.id.toString());
                } else {
                  // Page doesn't exist in state, add it with active flag
                  const pageForState = {
                    id: newPage.id.toString(),
                    name: newPage.title || pageName!,
                    content: response.session.page_html,
                    isActive: true
                  };
                  actions.addPage(pageForState);
                }

                actions.addMessage({
                  role: 'assistant',
                  content: `✅ Successfully created and switched to "${pageName}" page!`,
                  timestamp: new Date()
                });
              }
            }
          }
        }, 2000); // Wait a bit for auto-save to complete

        console.log('🔗 Element-based page created successfully. Use Link Pages button to add navigation.');

      } catch (error) {
        console.error('❌ Error creating page:', error);
        actions.addMessage({
          role: 'assistant',
          content: `❌ Failed to create page. Please try again.`,
          timestamp: new Date()
        });
      }
    } else {
      // Handle inline or modal implementation using editContent (includes conversation history)
      try {
        // Create intent and user messages to include in conversation history
        const additionalMessages: ChatMessage[] = [];

        // Add intent message if available
        if (state.selectedElement.intentData) {
          additionalMessages.push({
            role: 'assistant',
            content: `🎯 **Intent Analysis:**\n${state.selectedElement.intentData.userIntent}\n\n💡 **Suggestion:**\n${state.selectedElement.intentData.suggestion || "I can help implement this functionality."}`,
            timestamp: new Date()
          });
        }

        // Add user message
        const userMessage: ChatMessage = {
          role: 'user',
          content: `Implement "${elementText}" as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}`,
          timestamp: new Date()
        };
        additionalMessages.push(userMessage);

        // Add messages to chat UI
        additionalMessages.forEach(msg => actions.addMessage(msg));

        // Create implementation prompt that references the intent analysis
        const intentContext = state.selectedElement.intentData
          ? `Based on the intent analysis above: "${state.selectedElement.intentData.userIntent}"

${state.selectedElement.intentData.suggestion || "Please implement this functionality."}

` : '';

        const implementationPrompt = `${intentContext}Please implement "${elementText}" as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}.

Context: User clicked on an element and wants to implement functionality. Please modify the existing content to add this feature while preserving the current design and layout.

Implementation type: ${choice}
Element type: ${elementType}

${choice === 'modal' ? 'Create a modal/popup that opens when the element is clicked.' : 'Add the functionality directly to the current page.'}

Important: The intent analysis and suggestion above should guide your implementation approach.`;

        // Use editContent with explicit conversation history including intent
        await actions.editContent(implementationPrompt, additionalMessages);

        actions.addMessage({
          role: 'assistant',
          content: `✅ Successfully implemented "${elementText}" as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}!`,
          timestamp: new Date()
        });

      } catch (error) {
        console.error('Implementation error:', error);
        actions.addMessage({
          role: 'assistant',
          content: `❌ Failed to implement "${elementText}". Please try again.`,
          timestamp: new Date()
        });
      }
    }
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const formatPlanForDisplay = (plan: any): string => {
    if (typeof plan === 'string') {
      return plan;
    }

    if (plan && typeof plan === 'object') {
      let planContent = '';

      // Add overview
      if (plan.overview) {
        planContent += `${plan.overview}\n\n`;
      }

      // Add sections
      if (plan.sections && Array.isArray(plan.sections)) {
        plan.sections.forEach((section: any, index: number) => {
          planContent += `${index + 1}. ${section.title}\n`;
          if (section.description) {
            planContent += `${section.description}\n`;
          }
          if (section.details && Array.isArray(section.details)) {
            section.details.forEach((detail: string) => {
              planContent += `• ${detail}\n`;
            });
          }
          planContent += '\n';
        });
      }

      // Add features
      if (plan.features && Array.isArray(plan.features)) {
        planContent += `Key Features:\n`;
        plan.features.forEach((feature: string) => {
          planContent += `• ${feature}\n`;
        });
        planContent += '\n';
      }

      // Add accessibility
      if (plan.accessibility && Array.isArray(plan.accessibility)) {
        planContent += `Accessibility:\n`;
        plan.accessibility.forEach((item: string) => {
          planContent += `• ${item}\n`;
        });
      }

      return planContent.trim();
    }

    return '';
  };

  // All page creation utility functions are now handled by the new services

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className="h-screen bg-gray-50 flex w-full">
      {/* Resizable Pages Panel */}
      {!pagesPanelCollapsed && (
        <>
          <div
            className="flex-shrink-0 bg-white border-r border-gray-200 relative"
            style={{ width: pagesPanelWidth }}
          >
            {/* Project Pages List */}
            <div className="h-full flex flex-col">
              {/* Header */}
              <div className="px-4 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h2 className="text-lg font-semibold text-gray-900">Project Pages</h2>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">
                      {projectPages.length} page{projectPages.length !== 1 ? 's' : ''}
                    </span>
                    <button
                      onClick={refreshProjectPages}
                      className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                      title="Refresh pages"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Current Page Indicator */}
                {currentSessionId && (
                  <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    Currently viewing page
                  </div>
                )}

                {/* Create New Page Button */}
                <button
                  onClick={handleCreateNewPage}
                  className="w-full mt-3 flex items-center justify-center px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Create New Page
                </button>
              </div>

              {/* Pages List */}
              <div className="flex-1 overflow-y-auto p-4">
                {isLoadingProjectPages ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-3"></div>
                    <span className="text-sm text-gray-500">Loading pages...</span>
                  </div>
                ) : projectPages.length > 0 ? (
                  <div className="space-y-2">
                    {projectPages.map((page) => (
                      <div key={page.id} className="relative">
                        {renamingPageId === page.id ? (
                          /* Rename Input */
                          <div className="p-3 bg-white border border-blue-300 rounded-lg">
                            <input
                              type="text"
                              value={newPageName}
                              onChange={(e) => setNewPageName(e.target.value)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  confirmRename(page.id);
                                } else if (e.key === 'Escape') {
                                  cancelRename();
                                }
                              }}
                              className="w-full text-sm font-medium border-0 focus:outline-none focus:ring-0 p-0"
                              autoFocus
                            />
                            <div className="flex items-center justify-end space-x-2 mt-2">
                              <button
                                onClick={cancelRename}
                                className="px-2 py-1 text-xs text-gray-500 hover:text-gray-700"
                              >
                                Cancel
                              </button>
                              <button
                                onClick={() => confirmRename(page.id)}
                                className="px-2 py-1 text-xs text-blue-600 hover:text-blue-800 font-medium"
                              >
                                Save
                              </button>
                            </div>
                          </div>
                        ) : (
                          /* Regular Page Card */
                          <div className={`relative group rounded-lg border transition-colors ${
                            currentSessionId === page.id
                              ? 'bg-blue-50 border-blue-200 shadow-sm'
                              : 'bg-white border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                          }`}>
                            <button
                              onClick={() => handleProjectPageSelect(page)}
                              disabled={isLoadingPage}
                              className={`w-full text-left p-3 ${isLoadingPage ? 'opacity-50 cursor-not-allowed' : ''}`}
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1 min-w-0">
                                  <h3 className={`text-sm font-medium truncate ${
                                    currentSessionId === page.id ? 'text-blue-900' : 'text-gray-900'
                                  }`}>
                                    {page.title || `Page ${page.id}`}
                                  </h3>
                                  {page.url && (
                                    <p className="text-xs text-gray-500 truncate mt-1">
                                      {page.url}
                                    </p>
                                  )}
                                  <div className="flex items-center space-x-2 mt-1">
                                    <span className={`text-xs ${
                                      currentSessionId === page.id ? 'text-blue-600' : 'text-green-600'
                                    }`}>
                                      {page.status || 'Ready'}
                                    </span>
                                    <span className="text-xs text-gray-400">•</span>
                                    <span className="text-xs text-gray-400">
                                      {new Date(page.updated_at).toLocaleDateString()}
                                    </span>
                                  </div>
                                </div>
                                {currentSessionId === page.id && (
                                  <div className="ml-2 w-2 h-2 bg-blue-600 rounded-full"></div>
                                )}
                              </div>
                            </button>

                            {/* Dropdown Menu Button */}
                            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setOpenDropdownId(openDropdownId === page.id ? null : page.id);
                                }}
                                className="dropdown-button p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                </svg>
                              </button>

                              {/* Dropdown Menu */}
                              {openDropdownId === page.id && (
                                <div
                                  className="dropdown-menu absolute right-0 top-8 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-10"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      e.preventDefault();
                                      console.log('🔄 Rename clicked for page:', page.id);
                                      // Use setTimeout to ensure the click is processed before any state changes
                                      setTimeout(() => {
                                        startRename(page);
                                      }, 0);
                                    }}
                                    className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center"
                                  >
                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    Rename
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      e.preventDefault();
                                      console.log('🗑️ Delete clicked for page:', page.id);
                                      // Use setTimeout to ensure the click is processed before any state changes
                                      setTimeout(() => {
                                        startDelete(page.id);
                                      }, 0);
                                    }}
                                    className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center"
                                  >
                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    Delete
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="w-8 h-8 text-gray-300 mx-auto mb-3">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <p className="text-sm text-gray-500">No pages in this project yet</p>
                    <p className="text-xs text-gray-400 mt-1">
                      Create your first page to get started
                    </p>
                  </div>
                )}
              </div>
            </div>
            {/* Collapse button */}
            <button
              onClick={() => setPagesPanelCollapsed(true)}
              className="absolute top-4 right-4 p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors z-10"
              title="Collapse Pages Panel"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          </div>
          {/* Pages Resize Handle */}
          <div
            className="w-1 bg-gray-200 hover:bg-blue-400 cursor-col-resize transition-colors flex-shrink-0"
            onMouseDown={handleMouseDown('pages')}
            title="Drag to resize pages panel"
          />
        </>
      )}

      {/* Collapsed Pages Toggle */}
      {pagesPanelCollapsed && (
        <div className="w-12 flex-shrink-0 bg-white border-r border-gray-200 flex flex-col items-center py-4">
          <button
            onClick={() => setPagesPanelCollapsed(false)}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            title="Expand Pages Panel"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
          <div className="mt-4 text-xs text-gray-400 transform -rotate-90 whitespace-nowrap">
            Pages
          </div>
          {projectPages.length > 0 && (
            <div className="mt-2 w-5 h-5 bg-blue-100 text-blue-600 text-xs font-medium rounded-full flex items-center justify-center">
              {projectPages.length}
            </div>
          )}
          {currentSessionId && (
            <div className="mt-4 w-2 h-2 bg-blue-600 rounded-full" title="Current page"></div>
          )}
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex w-full">
        {/* Intent-Based Editor or New Page Creation */}
        <div className="flex-1 w-full">
          {(() => {
            const shouldShowPrompt = isCreatingNewPage || (projectId && projectPages.length === 0 && !isLoadingProjectPages);
            const shouldShowCodePreview = state.isGenerating;

            return shouldShowPrompt && !shouldShowCodePreview;
          })() ? (
            showPlanReview ? (
              /* Plan Review UI */
              <div className="h-full flex flex-col justify-center px-4 bg-gray-50">
                <div className="w-full max-w-4xl mx-auto">
                  {/* Header */}
                  <div className="text-center mb-6">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">Review Your Page Plan</h1>
                    <p className="text-gray-600">
                      Page: <span className="font-semibold text-blue-600">{generatedPageName || 'New Page'}</span>
                    </p>
                  </div>

                  {/* Plan Display */}
                  <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 mb-6">
                    <div className="prose max-w-none">
                      <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">
                        {generatedPlan ? formatPlanForDisplay(generatedPlan) : 'Loading plan...'}
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-center space-x-4">
                    <button
                      onClick={handleRejectPlan}
                      className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50 font-medium"
                    >
                      ← Edit Prompt
                    </button>
                    <button
                      onClick={handleGeneratePageFromPlan}
                      disabled={state.isGenerating}
                      className="flex items-center space-x-2 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                    >
                      {state.isGenerating ? (
                        <>
                          <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span>Generating Page...</span>
                        </>
                      ) : (
                        <>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                          <span>Generate Page</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              /* New Page Creation UI - Using PromptInputPageV3 style */
              <div className="h-full flex flex-col justify-center px-4 bg-gray-50">
              <style>{`
                .no-scrollbar::-webkit-scrollbar {
                  display: none;
                }
                .no-scrollbar {
                  -ms-overflow-style: none;
                  scrollbar-width: none;
                }
              `}</style>

              <div className="w-full max-w-3xl mx-auto">
                {/* Header */}
                <div className="text-center mb-4">
                  <h1 className="text-4xl font-bold text-gray-900">
                    {projectPages.length === 0 && !isCreatingNewPage ? (
                      <>
                        Create your first page
                        <span className="block text-blue-600">for this project</span>
                      </>
                    ) : (
                      <>
                        What would you like to
                        <span className="block text-blue-600">create today?</span>
                      </>
                    )}
                  </h1>
                </div>

                {/* Prompt Input - Main Focus Area */}
                <form onSubmit={(e) => { e.preventDefault(); handleNewPagePromptSubmit(); }} className="mb-3">
                  <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
                    <textarea
                      value={newPagePrompt}
                      onChange={(e) => setNewPagePrompt(e.target.value)}
                      placeholder="Describe your new page in detail...

For example: 'A login page with email and password fields, forgot password link, and social login options. Use a clean, modern design with blue accents.'"
                      className="w-full h-40 px-6 py-4 text-gray-900 placeholder-gray-500 border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 no-scrollbar text-base leading-relaxed"
                      disabled={state.isGenerating}
                      autoFocus
                    />

                    {/* Preview of generated name and Submit */}
                    <div className="px-6 py-3 bg-gray-50 border-t border-gray-100">
                      {/* Generated page name preview */}
                      {/* {newPagePrompt.trim() && (
                        <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <div className="flex items-center">
                            <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <span className="text-sm font-medium text-blue-900">
                              Page will be named: "{generatedPageName || 'New Page'}"
                            </span>
                          </div>
                        </div>
                      )} */}

                      {/* Action buttons */}
                      <div className="flex items-center justify-between">
                        {projectPages.length > 0 && (
                          <button
                            type="button"
                            onClick={handleCancelNewPage}
                            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
                          >
                            Cancel
                          </button>
                        )}

                        <button
                          type="submit"
                          disabled={!newPagePrompt.trim() || isGeneratingPlan}
                          className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                        >
                          {isGeneratingPlan ? (
                            <>
                              <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              <span>Generating Plan...</span>
                            </>
                          ) : (
                            <>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              <span>Generate Plan</span>
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </form>

                {/* Examples - Compact */}
                <div className="text-center">
                  <p className="text-xs text-gray-500 mb-1">Quick examples:</p>
                  <div className="flex flex-wrap justify-center gap-1 max-w-4xl mx-auto">
                    {[
                      'Login page with email and password fields',
                      'Pricing page with three subscription tiers',
                      'Contact form with company information',
                      'About page with team member profiles'
                    ].map((example, index) => (
                      <button
                        key={index}
                        onClick={() => setNewPagePrompt(example)}
                        className="px-2 py-1 text-xs text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors disabled:opacity-50"
                        disabled={state.isGenerating}
                      >
                        {example}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            )
          ) : (
            /* Intent-Based Editor - Show code/preview during generation or normal editing */
            <IntentBasedEditor
            prototypeId={projectId || 1} // Use projectId from location state
            userId={1} // TODO: Get from auth context
            initialHtml={state.htmlContent || state.stableIframeContent || ''}
            pageUrl={window.location.href}
            isCreateMode={initialGeneration || !location.pathname.includes('/editor/')} // Use create mode for new prototypes
            elementSelectorActive={elementSelectorActive}
            isGenerating={state.isGenerating || isLoadingPage} // Pass generation state for progress indicators
            streamingContent={isLoadingPage ? 'Loading existing page...' : state.streamingContent} // Pass streaming content for real-time updates
            operationProgress={state.operationProgress} // Pass progress data for enhanced progress indicators
            onElementSelected={(element) => {
              // When element is selected, show the main implementation modal
              actions.setSelectedElement(element);
              actions.setShowImplementModal(true);
              setElementSelectorActive(false); // Turn off selector after selection
            }}
            onHtmlChange={(html) => {
              // Update both content states when IntentBasedEditor changes HTML
              actions.setHtmlContent(html);
              actions.setStableIframeContent(html);

              // Update current page content if we have a valid page ID
              if (state.currentPageId) {
                actions.updatePage(state.currentPageId, { content: html });
              }
            }}
            onError={(error) => {
              actions.addMessage({
                role: 'assistant',
                content: `❌ Error: ${error}`,
                timestamp: new Date()
              });
            }}
            onClose={() => {
              // Navigate back to My Prototypes or previous page
              navigate('/my-prototypes');
            }}
            onAddChatMessage={(message) => {
              // Add intent messages to chat
              actions.addMessage(message);
            }}
            onEditContent={async (prompt, additionalMessages) => {
              // Use editContent for intent-based implementations
              await actions.editContent(prompt, additionalMessages);
            }}
            onIntentGenerationChange={(generating) => {
              // Update intent generation state for progress indicator
              setIsGeneratingIntent(generating);
            }}
          />
          )}
        </div>

        {/* Chat Resize Handle - Hide during generation */}
        {!state.isGenerating && (
          <div
            className="w-1 bg-gray-200 hover:bg-blue-400 cursor-col-resize transition-colors flex-shrink-0"
            onMouseDown={handleMouseDown('chat')}
            title="Drag to resize chat panel"
          />
        )}

        {/* Resizable Chat Interface - Hide during generation */}
        {!state.isGenerating && (
          <div
            className="flex-shrink-0 bg-white border-l border-gray-200"
            style={{ width: chatPanelWidth }}
          >
            <ChatInterface
              messages={state.messages}
              input={state.input}
              isGenerating={state.isGenerating}
              onInputChange={actions.setInput}
              onSubmit={handleChatSubmit}
              onToggleSelector={() => {
                setElementSelectorActive(!elementSelectorActive);
              }}
              isSelectorActive={elementSelectorActive}
            />
          </div>
        )}
      </div>

      {/* Page Creation Confirmation Dialog */}
      {showPageCreationDialog && pendingPageCreation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Create New Page</h3>
            </div>

            <p className="text-gray-600 mb-2">
              You clicked on a navigation link for <strong>"{pendingPageCreation.pageName}"</strong>
            </p>
            <p className="text-sm text-gray-500 mb-6">
              This page doesn't exist yet. Would you like me to create it with relevant content?
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6">
              <p className="text-sm text-blue-800">
                <strong>What I'll do:</strong>
              </p>
              <ul className="text-sm text-blue-700 mt-1 space-y-1">
                <li>• Create a new "{pendingPageCreation.pageName}" page</li>
                <li>• Generate relevant content for this page type</li>
                <li>• Maintain consistent design with your existing pages</li>
                <li>• Add proper navigation links</li>
              </ul>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelPageCreation}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  console.log('🔘 Create Page button clicked');
                  confirmPageCreation();
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create Page
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Linking Suggestion Dialog */}
      {showLinkingDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Link Your Pages</h3>
            </div>

            <p className="text-gray-600 mb-2">
              Great! You now have multiple pages.
            </p>
            <p className="text-sm text-gray-500 mb-6">
              Would you like me to update the navigation on all pages so visitors can easily navigate between them?
            </p>

            <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-6">
              <p className="text-sm text-green-800">
                <strong>What I'll do:</strong>
              </p>
              <ul className="text-sm text-green-700 mt-1 space-y-1">
                <li>• Update navigation bars on all pages</li>
                <li>• Add links to all your pages</li>
                <li>• Maintain consistent styling</li>
                <li>• Preserve all existing content</li>
              </ul>
            </div>

            <div className="flex justify-between items-center">
              <button
                onClick={() => {
                  setShowLinkingDialog(false);
                  setHasShownLinkingSuggestion(true); // Prevent showing again
                }}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
              >
                Don't show again
              </button>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowLinkingDialog(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Maybe Later
                </button>
                <button
                  onClick={handleLinkPages}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                  Link Pages
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Linking Progress Modal */}
      {linkingProgress && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-blue-600 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Linking Pages</h3>
            </div>

            <p className="text-gray-600 mb-4">
              Updating navigation on all pages...
            </p>

            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Progress</span>
                <span>{linkingProgress.current} of {linkingProgress.total}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(linkingProgress.current / linkingProgress.total) * 100}%` }}
                ></div>
              </div>
            </div>

            {linkingProgress.currentPage && (
              <p className="text-sm text-gray-500">
                Currently updating: <strong>{linkingProgress.currentPage}</strong>
              </p>
            )}
          </div>
        </div>
      )}

      {/* Implementation Choice Modal */}
      {state.showImplementModal && state.selectedElement && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-lg w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">Implement Feature</h3>
                <button
                  onClick={() => actions.setShowImplementModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">"{state.selectedElement.textContent}"</p>
                    <p className="text-sm text-gray-500">{state.selectedElement.implementationReason}</p>
                  </div>
                </div>

                {/* Intent Generation Progress */}
                {isGeneratingIntent && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-3"></div>
                      <div>
                        <p className="text-sm font-medium text-blue-900">Analyzing element...</p>
                        <p className="text-xs text-blue-700">Understanding what you want to implement</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-900 mb-3">How would you like to implement this?</h4>
                <div className="space-y-3">
                  {/* Inline Implementation */}
                  <button
                    onClick={() => handleImplementChoice('inline')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-green-200">
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Modify Current Page</h5>
                        <p className="text-sm text-gray-500">Add functionality directly to this page</p>
                      </div>
                    </div>
                  </button>

                  {/* Modal Implementation */}
                  <button
                    onClick={() => handleImplementChoice('modal')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-200">
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Create Modal/Popup</h5>
                        <p className="text-sm text-gray-500">Open content in an overlay window</p>
                      </div>
                    </div>
                  </button>

                  {/* New Page Implementation */}
                  <button
                    onClick={() => handleImplementChoice('page')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-200">
                        <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Create New Page</h5>
                        <p className="text-sm text-gray-500">Navigate to a dedicated page for this feature</p>
                      </div>
                    </div>
                  </button>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  onClick={() => actions.setShowImplementModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Delete Page</h3>
            </div>

            <p className="text-gray-600 mb-2">
              Are you sure you want to delete this page?
            </p>
            <p className="text-sm text-gray-500 mb-6">
              This action cannot be undone. All content will be permanently lost.
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelDelete}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => confirmDelete(showDeleteConfirm)}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Delete Page
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default EditorPageV3Refactored;
