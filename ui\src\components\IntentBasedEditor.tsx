import React, { useState, useCallback, useRef, useEffect } from 'react';
import ElementSelector from './ElementSelector';
import IntentDisplay from './IntentDisplay';
import IntentMessage from './IntentMessage';
import EditConfirmation from './EditConfirmation';
import CodeEditor from './CodeEditor';
import { generateIntent, Intent } from '../services/intentApiService';
import '../styles/elementSelection.css';
import '../styles/intentDisplay.css';

interface IntentBasedEditorProps {
  prototypeId?: number;
  userId?: number;
  initialHtml: string;
  pageUrl: string;
  onHtmlChange: (html: string) => void;
  onError?: (error: string) => void;
  onClose?: () => void; // Function to call when user wants to exit editing mode
  elementSelectorActive?: boolean; // Whether element selection is active from ChatInterface
  onElementSelected?: (element: any) => void; // Callback when element is selected
  isCreateMode?: boolean; // New flag to skip database for create flow
  isGenerating?: boolean; // External generation state for progress indicators
  streamingContent?: string; // External streaming content for real-time updates
  onAddChatMessage?: (message: any) => void; // Callback to add messages to chat
  onEditContent?: (prompt: string, additionalMessages?: any[]) => Promise<void>; // Callback to edit content using parent's system
  onIntentGenerationChange?: (generating: boolean) => void; // Callback to update intent generation state
}

interface EditResult {
  success: boolean;
  html?: string;
  error?: string;
  tokensUsed?: number;
  processingTime?: number;
}

interface StreamingProgress {
  stage: string;
  progress: number;
  message?: string;
}

const IntentBasedEditor: React.FC<IntentBasedEditorProps> = ({
  prototypeId,
  userId,
  initialHtml,
  pageUrl,
  onHtmlChange,
  onError,
  onClose,
  elementSelectorActive = false,
  onElementSelected,
  isCreateMode = false,
  isGenerating = false,
  streamingContent = '',
  onAddChatMessage,
  onEditContent,
  onIntentGenerationChange
}) => {
  // State management
  const [currentHtml, setCurrentHtml] = useState(initialHtml);
  const [session, setSession] = useState<SessionData | null>(null);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [currentIntent, setCurrentIntent] = useState<Intent | null>(null);
  const [isGeneratingIntent, setIsGeneratingIntent] = useState(false);
  const [isProcessingEdit, setIsProcessingEdit] = useState(false);
  const [editResult, setEditResult] = useState<EditResult | null>(null);

  // Refs for stable iframe updates
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const lastContentRef = useRef<string>('');
  const [streamingProgress, setStreamingProgress] = useState<StreamingProgress | null>(null);
  const [showImplementationModal, setShowImplementationModal] = useState(false);

  // Use external element selector state instead of internal mode
  const isElementMode = elementSelectorActive;

  // View mode for preview/code toggle
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('preview');

  // Refs
  const sessionInitialized = useRef(false);

  // Initialize session on component mount (skip for create mode)
  useEffect(() => {
    if (!sessionInitialized.current && !isCreateMode) {
      initializeSession();
      sessionInitialized.current = true;
    } else if (isCreateMode) {
      // For create mode, set a mock session to enable functionality
      setSession({
        id: 'create-mode-session',
        prototypeId: prototypeId || 0,
        userId: userId || 0,
        pageUrl,
        pageHtml: currentHtml,
        sessionState: 'active',
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      } as SessionData);
      sessionInitialized.current = true;
    }
  }, [isCreateMode]);

  // Helper function to extract HTML from response
  const extractHtmlFromResponse = (response: string): string => {
    if (!response) return '';

    // First, check if this is already valid HTML
    if (response.trim().startsWith('<!DOCTYPE html') || response.trim().startsWith('<html')) {
      return response.trim();
    }

    // Look for HTML content between ```html and ``` markers (non-greedy)
    const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
    if (htmlMatch) {
      return htmlMatch[1].trim();
    }

    // Look for HTML starting with DOCTYPE or html tag (capture everything)
    const doctypeMatch = response.match(/(<!DOCTYPE html[\s\S]*)/i);
    if (doctypeMatch) {
      return doctypeMatch[1].trim();
    }

    const htmlTagMatch = response.match(/(<html[\s\S]*)/i);
    if (htmlTagMatch) {
      return htmlTagMatch[1].trim();
    }

    // If response contains HTML tags, assume it's HTML and take everything from first tag
    if (response.includes('<') && response.includes('>')) {
      const firstTagIndex = response.indexOf('<');
      return response.substring(firstTagIndex).trim();
    }

    // If no HTML patterns found, wrap in basic HTML structure
    if (response.trim()) {
      return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Content</title>
</head>
<body>
    <div style="padding: 20px; font-family: Arial, sans-serif;">
        <pre style="white-space: pre-wrap; word-wrap: break-word;">${response.trim()}</pre>
    </div>
</body>
</html>`;
    }

    return response.trim();
  };

  // Simple HTML update - just show the content
  useEffect(() => {
    let contentToUse = (isGenerating && streamingContent) ? streamingContent : initialHtml;

    if (contentToUse) {
      contentToUse = extractHtmlFromResponse(contentToUse);
    }

    if (contentToUse !== currentHtml && contentToUse.trim()) {
      setCurrentHtml(contentToUse);
    }
  }, [initialHtml, streamingContent, isGenerating]);

  // Update iframe content when HTML changes
  useEffect(() => {
    if (currentHtml && currentHtml !== lastContentRef.current) {
      lastContentRef.current = currentHtml;
      // Removed noisy log: Updating iframe with new content
    }
  }, [currentHtml]);

  /**
   * Initialize session for Readdy-style editing (only for edit mode)
   */
  const initializeSession = async () => {
    if (isCreateMode) return; // Skip for create mode

    try {
      const response = await intentApiService.createSession(
        prototypeId!,
        userId!,
        pageUrl,
        currentHtml
      );

      if (response.success && response.data) {
        setSession(response.data);
        console.log('Session initialized:', response.data.id);
      } else {
        onError?.('Failed to initialize editing session');
      }
    } catch (error) {
      console.error('Session initialization error:', error);
      onError?.('Failed to initialize editing session');
    }
  };

  /**
   * Handle user input submission
   */
  const handleUserInputSubmission = useCallback(async (input: string) => {
    if (!session) {
      onError?.('No active session. Please refresh the page.');
      return;
    }

    if (!input.trim()) {
      onError?.('Please enter a request.');
      return;
    }

    // Prevent duplicate calls
    if (isGeneratingIntent) {
      console.log('🔥 Intent generation already in progress, ignoring duplicate call');
      return;
    }

    // Immediate UI feedback
    setIsGeneratingIntent(true);
    setCurrentIntent(null);
    setEditResult(null);

    try {
      // Generate intent using session-based editing API
      const response = await fetch('/api/llm/v3/edit-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          sessionId: session.id,
          userQuery: input.trim(),
          pageUrl: session.pageUrl
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      let accumulatedHtml = '';
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        accumulatedHtml += chunk;

        // Update HTML in real-time
        setCurrentHtml(accumulatedHtml);
        onHtmlChange(accumulatedHtml);
      }

      // Mark as successful
      setEditResult({
        success: true,
        html: accumulatedHtml
      });

    } catch (error) {
      console.error('User input processing error:', error);
      setEditResult({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process request'
      });
      onError?.(error instanceof Error ? error.message : 'Failed to process request');
    } finally {
      setIsGeneratingIntent(false);
    }
  }, [session, isGeneratingIntent, onError, onHtmlChange]);

  /**
   * Handle element selection from ElementSelector
   */
  const handleElementSelection = useCallback(async (selector: string) => {
    // Extract element info and pass to parent
    const parser = new DOMParser();
    const doc = parser.parseFromString(currentHtml, 'text/html');
    let element = doc.querySelector(selector);

    if (!element) {
      // Try simplified selector as fallback
      const simplifiedSelector = selector.split(' ').pop() || selector;
      element = doc.querySelector(simplifiedSelector);
    }

    if (element && onElementSelected) {
      // Create element info object similar to what EditorPageV3Refactored expects
      const elementInfo = {
        textContent: element.textContent?.trim() || 'Element',
        tagName: element.tagName.toLowerCase(),
        implementationType: 'interactive',
        implementationReason: `Implement functionality for ${element.textContent?.trim() || 'this element'}`,
        selector: selector,
        outerHTML: element.outerHTML
      };

      // Show popup immediately - don't wait for intent generation
      onElementSelected(elementInfo);

      // Start intent generation in background with progress indicator
      setIsGeneratingIntent(true);
      onIntentGenerationChange?.(true); // Notify parent component
      setSelectedElement(element.textContent?.trim() || 'Element');

      try {
        const elementCode = element.outerHTML;
        const htmlContent = currentHtml;

        // Generate intent asynchronously
        const intentResult = await generateIntent({ htmlContent, elementCode });

        // Show intent in conversation/chat UI when ready
        if (intentResult.success && intentResult.intent) {
          setCurrentIntent(intentResult.intent);

          // Add intent to chat messages using the callback
          if (onAddChatMessage) {
            onAddChatMessage({
              role: "assistant",
              content: `🎯 **Intent Analysis:**\n${intentResult.intent.userIntent}\n\n💡 **Suggestion:**\n${intentResult.intent.suggestion || "I can help implement this functionality."}`,
              timestamp: new Date(),
              type: "intent"
            });
          }
        } else if (intentResult.error) {
          onError?.(intentResult.error);
        }
      } catch (error) {
        console.error('Intent generation failed:', error);
        onError?.('Failed to analyze element intent');
      } finally {
        setIsGeneratingIntent(false);
        onIntentGenerationChange?.(false); // Notify parent component
      }
    } else {
      onError?.('Could not find the selected element');
    }
  }, [currentHtml, onElementSelected, onError, onIntentGenerationChange, onAddChatMessage]);

  /**
   * Handle intent confirmation - use editContent instead of implementation API
   */
  const handleIntentConfirmation = useCallback(async (intent: Intent, userQuery?: string) => {
    if (!currentIntent) return;

    // Create the intent and user messages that need to be included
    const intentMessage = {
      role: "assistant" as const,
      content: `🎯 **Intent Analysis:**\n${currentIntent.userIntent}\n\n💡 **Suggestion:**\n${currentIntent.suggestion || "I can help implement this functionality."}`,
      timestamp: new Date(),
      type: "intent"
    };

    const userMessage = {
      role: "user" as const,
      content: `Implement: ${currentIntent.userIntent}`,
      timestamp: new Date()
    };

    // Add messages to chat UI
    if (onAddChatMessage) {
      onAddChatMessage(intentMessage);
      onAddChatMessage(userMessage);
    }

    // Create a comprehensive prompt that includes the intent for better context
    const implementationPrompt = `Based on the intent analysis above, please implement the following:

${currentIntent.userIntent}

${currentIntent.suggestion || "Please implement this functionality."}

Context: User clicked on an element and wants to implement functionality. Please modify the existing content to add this feature while preserving the current design and layout.

Important: The intent analysis and suggestion above should guide your implementation approach.`;

    // Use editContent with explicit conversation history including intent
    if (onEditContent) {
      try {
        // Pass the intent and user messages explicitly to ensure they're included
        await onEditContent(implementationPrompt, [intentMessage, userMessage]);

        // Clear intent state after successful implementation
        setCurrentIntent(null);
        setSelectedElement(null);
      } catch (error) {
        onError?.(`Failed to implement: ${error}`);
      }
    } else {
      // Fallback: show implementation modal if editContent not available
      setShowImplementationModal(true);
    }
  }, [currentIntent, onAddChatMessage, onEditContent, onError]);

  /**
   * Handle implementation choice (inline/modal/page) - USE EDITCONTENT FOR CONSISTENCY
   */
  const handleImplementationChoice = useCallback(async (choice: 'inline' | 'modal' | 'page') => {
    if (!currentIntent) return;

    // Create the intent and user messages that need to be included
    const intentMessage = {
      role: "assistant" as const,
      content: `🎯 **Intent Analysis:**\n${currentIntent.userIntent}\n\n💡 **Suggestion:**\n${currentIntent.suggestion || "I can help implement this functionality."}`,
      timestamp: new Date(),
      type: "intent"
    };

    const userMessage = {
      role: "user" as const,
      content: `Implement "${currentIntent.userIntent}" as ${choice === 'inline' ? 'inline functionality' : choice === 'modal' ? 'a modal/popup' : 'a new page'}`,
      timestamp: new Date()
    };

    // Add messages to chat UI
    if (onAddChatMessage) {
      onAddChatMessage(intentMessage);
      onAddChatMessage(userMessage);
    }

    // Create implementation prompt that includes the intent context
    const implementationPrompt = `Based on the intent analysis above, please implement the following:

${currentIntent.userIntent}

${currentIntent.suggestion || "Please implement this functionality."}

Context: User clicked on an element and wants to implement functionality. Please modify the existing content to add this feature while preserving the current design and layout.

Implementation type: ${choice}
${choice === 'modal' ? 'Create a modal/popup that opens when the element is clicked.' : choice === 'inline' ? 'Add the functionality directly to the current page.' : 'Create a new page for this functionality.'}

Important: The intent analysis and suggestion above should guide your implementation approach.`;

    // Use editContent with explicit conversation history including intent
    if (onEditContent) {
      try {
        // Pass the intent and user messages explicitly to ensure they're included
        await onEditContent(implementationPrompt, [intentMessage, userMessage]);

        // Clear intent state after successful implementation
        setCurrentIntent(null);
        setSelectedElement(null);
      } catch (error) {
        onError?.(`Failed to implement: ${error}`);
      }
    } else {
      // Fallback: show implementation modal if editContent not available
      setShowImplementationModal(true);
    }
  }, [currentIntent, onAddChatMessage, onEditContent, onError]);

  /**
   * Handle accepting the implementation
   */
  const handleAcceptImplementation = useCallback(() => {
    // Changes are already applied, just clear the result
    setEditResult(null);
    setCurrentIntent(null);
    setSelectedElement(null);
  }, []);

  /**
   * Handle rejecting the implementation
   */
  const handleRejectImplementation = useCallback(() => {
    // Revert to original HTML
    if (editResult?.html) {
      // We don't have the original HTML stored, so we'll just clear the result
      // In a real implementation, you'd want to store the original HTML
      setEditResult(null);
      setCurrentIntent(null);
      setSelectedElement(null);
    }
  }, [editResult]);

  /**
   * Handle retrying the implementation
   */
  const handleRetryImplementation = useCallback(() => {
    if (currentIntent) {
      setEditResult(null);
      setShowImplementationModal(true);
    }
  }, [currentIntent]);

  /**
   * Handle intent rejection
   */
  const handleIntentRejection = useCallback(() => {
    setCurrentIntent(null);
    setSelectedElement(null);
    setEditResult(null);
  }, []);

  /**
   * Handle intent refinement
   */
  const handleIntentRefinement = useCallback(async (refinedQuery: string) => {
    if (!currentIntent) return;

    // Re-trigger intent generation with refined query
    // For now, we'll just update the intent suggestion
    setCurrentIntent({
      ...currentIntent,
      suggestion: refinedQuery
    });
  }, [currentIntent]);

  /**
   * Handle edit acceptance
   */
  const handleEditAcceptance = useCallback(() => {
    if (editResult?.success && editResult.html) {
      setCurrentHtml(editResult.html);
      onHtmlChange(editResult.html);

      // Reset state
      setCurrentIntent(null);
      setSelectedElement(null);
      setEditResult(null);
    }
  }, [editResult, onHtmlChange]);

  /**
   * Handle edit rejection (undo)
   */
  const handleEditRejection = useCallback(() => {
    setEditResult(null);
    setCurrentIntent(null);
    setSelectedElement(null);
  }, []);

  /**
   * Handle edit retry
   */
  const handleEditRetry = useCallback(() => {
    if (currentIntent) {
      setEditResult(null);
      handleIntentConfirmation(currentIntent);
    }
  }, [currentIntent, handleIntentConfirmation]);

  return (
    <div className="intent-based-editor w-full h-full">
      {/* Main Preview Area - Always render */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden w-full h-full">
        {/* Professional Header with Toggle */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between">
          <h4 className="text-sm font-semibold text-gray-800">
            {viewMode === 'preview' ? 'Live Preview' : 'HTML Code'}
          </h4>
          <div className="flex items-center bg-white rounded-lg border border-gray-200 p-1">
            <button
              onClick={() => setViewMode('preview')}
              className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${
                viewMode === 'preview'
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <svg className="w-4 h-4 inline mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              Preview
            </button>
            <button
              onClick={() => setViewMode('code')}
              className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${
                viewMode === 'code'
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <svg className="w-4 h-4 inline mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
              </svg>
              Code
            </button>
          </div>
        </div>

        {/* Content Area */}
        <div className="relative flex-1 w-full h-full min-h-[500px]">
          {/* Improved Progress UX: Centered spinner only if no content, bottom bar if content */}
          {(() => {
            // Determine if there is preview content to show
            const previewContent =
              (isGenerating && streamingContent)
                ? extractHtmlFromResponse(streamingContent)
                : extractHtmlFromResponse(currentHtml);
            const hasPreviewContent =
              previewContent &&
              previewContent.length > 500 &&
              (previewContent.includes('</') || previewContent.length > 2000);

            // Always show progress bar while generating or processing, regardless of content
            if (isGeneratingIntent || isProcessingEdit || isGenerating) {
              if (!hasPreviewContent) {
                // No content yet: show centered spinner overlay
                return (
                  <div className="absolute inset-0 flex flex-col items-center justify-center bg-white/80 z-30">
                    <div className="flex flex-col items-center space-y-4">
                      <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                      <div className="text-center">
                        <p className="text-base font-semibold text-gray-900">
                          {isGenerating ? 'Generating Content...' : isGeneratingIntent ? 'Analyzing...' : 'Processing...'}
                        </p>
                        <p className="text-sm text-gray-600">
                          {isGenerating ? 'Creating your prototype' : isGeneratingIntent ? 'Understanding your request' : 'Implementing changes'}
                        </p>
                        {streamingProgress && (
                          <div className="w-48 bg-gray-200 rounded-full h-2 mt-4">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${streamingProgress.progress}%` }}
                            ></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              } else {
                // Content is visible: show only bottom progress bar
                return (
                  <div className="absolute bottom-0 left-0 right-0 bg-white/80 z-30 px-4 py-2 flex items-center">
                    <div className="flex-1">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${streamingProgress?.progress ?? 30}%` }}
                        ></div>
                      </div>
                    </div>
                    <span className="ml-3 text-xs text-gray-600">
                      {isGenerating ? 'Generating...' : isGeneratingIntent ? 'Analyzing...' : 'Processing...'}
                    </span>
                  </div>
                );
              }
            }
            // PATCH: If all flags are false and still no content, show fallback spinner/message
            if (!hasPreviewContent && !isGeneratingIntent && !isProcessingEdit && !isGenerating) {
              return (
                <div className="absolute inset-0 flex flex-col items-center justify-center bg-white/80 z-30">
                  <div className="flex flex-col items-center space-y-4">
                    <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <div className="text-center">
                      <p className="text-base font-semibold text-gray-900">
                        Still working...
                      </p>
                      <p className="text-sm text-gray-600">
                        Waiting for content from the server. If this persists, please try again.
                      </p>
                    </div>
                  </div>
                </div>
              );
            }
            return null;
          })()}

          {/* Content based on view mode */}
          {viewMode === 'code' ? (
            // Enhanced Code Editor with line numbers and syntax highlighting
            <div className="p-4 h-full">
              <CodeEditor
                value={extractHtmlFromResponse(currentHtml) || ''}
                onChange={(value) => {
                  setCurrentHtml(value);
                  onHtmlChange(value);
                }}
                language="html"
                placeholder="<!-- Enter your HTML code here -->"
                className="h-96"
              />
            </div>
          ) : (
            // Preview mode
            <>
              {isElementMode ? (
                // Element selection mode
                <ElementSelector
                  html={currentHtml}
                  onSelect={handleElementSelection}
                />
              ) : (isGenerating && streamingContent) || currentHtml ? (
                // Show streamingContent live during generation, otherwise show currentHtml
                <DebouncedIframe
                  ref={iframeRef}
                  isGenerating={isGenerating}
                  streamingContent={streamingContent}
                  currentHtml={currentHtml}
                  extractHtmlFromResponse={extractHtmlFromResponse}
                />
              ) : (
                // Empty state - only show when no content AND not generating
                <div className="flex items-center justify-center bg-gray-50 min-h-[500px]">
                  <div className="text-center text-gray-500 max-w-md">
                    <svg className="w-16 h-16 mx-auto mb-6 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Create</h3>
                    <p className="text-gray-600">Start a conversation in the chat to generate your prototype, or use the Selector to modify existing elements.</p>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Intent Display */}
      <IntentDisplay
        intent={currentIntent}
        isLoading={isGeneratingIntent}
        onConfirm={handleIntentConfirmation}
        onReject={handleIntentRejection}
        onRefine={handleIntentRefinement}
      />

      {/* Edit Confirmation */}
      <EditConfirmation
        isProcessing={isProcessingEdit}
        result={editResult}
        onAccept={handleAcceptImplementation}
        onReject={handleRejectImplementation}
        onRetry={handleRetryImplementation}
        streamingProgress={streamingProgress || undefined}
      />

      {/* Implementation Choice Modal */}
      {showImplementationModal && currentIntent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-lg w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">Choose Implementation</h3>
                <button
                  onClick={() => setShowImplementationModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-900 mb-3">How would you like to implement this feature?</h4>
                <div className="space-y-3">
                  {/* Inline Implementation */}
                  <button
                    onClick={() => handleImplementationChoice('inline')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-green-200">
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Inline Functionality</h5>
                        <p className="text-sm text-gray-500">Add functionality directly to this element</p>
                      </div>
                    </div>
                  </button>

                  {/* Modal Implementation */}
                  <button
                    onClick={() => handleImplementationChoice('modal')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-200">
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Modal/Popup</h5>
                        <p className="text-sm text-gray-500">Open functionality in a modal overlay</p>
                      </div>
                    </div>
                  </button>

                  {/* New Page Implementation */}
                  <button
                    onClick={() => handleImplementationChoice('page')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-200">
                        <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Create New Page</h5>
                        <p className="text-sm text-gray-500">Navigate to a dedicated page for this feature</p>
                      </div>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

/**
 * DebouncedIframe: Prevents iframe blinking by debouncing srcDoc updates.
 * (Moved React imports to top of file to avoid duplicate identifier error)
 */

const DebouncedIframe = React.forwardRef(function DebouncedIframe(
  {
    isGenerating,
    streamingContent,
    currentHtml,
    extractHtmlFromResponse,
  }: {
    isGenerating: boolean;
    streamingContent: string;
    currentHtml: string;
    extractHtmlFromResponse: (s: string) => string;
  },
  ref
) {
  const iframeRef = React.useRef<HTMLIFrameElement>(null);
  const lastContentRef = React.useRef<string>('');
  const debounceTimeout = React.useRef<any>(null);

  // Compute the content to show
  const getContent = () => {
    if (isGenerating && streamingContent) {
      const content = extractHtmlFromResponse(streamingContent);
      // Just show the content - your 14,666 chars should display
      if (content.length < 100) {
        return '<!DOCTYPE html><html><body><div style="padding:20px;text-align:center;color:#666;">Loading content...</div></body></html>';
      }
      return content;
    }
    if (isGenerating && !currentHtml) {
      return `<!DOCTYPE html>
<html>
<head>
  <title>Generating...</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0; padding: 40px; background: #f8fafc;
      display: flex; align-items: center; justify-content: center;
      min-height: 100vh; text-align: center; color: #64748b;
    }
    .spinner {
      width: 40px; height: 40px; border: 3px solid #e2e8f0;
      border-top: 3px solid #3b82f6; border-radius: 50%;
      animation: spin 1s linear infinite; margin: 0 auto 20px;
    }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    h2 { color: #1e293b; margin-bottom: 8px; }
    p { margin: 0; }
  </style>
</head>
<body>
  <div>
    <div class="spinner"></div>
    <h2>Generating Your Prototype</h2>
    <p>Please wait while we create your content...</p>
  </div>
</body>
</html>`;
    }
    // Otherwise, show the currentHtml
    const content = extractHtmlFromResponse(currentHtml);
    if (content.length < 100) {
      return '<!DOCTYPE html><html><body><div style="padding:20px;text-align:center;color:#666;">Loading content...</div></body></html>';
    }
    return content;
  };

  // Debounced update of srcDoc
  React.useEffect(() => {
    const content = getContent();
    if (content !== lastContentRef.current) {
      if (debounceTimeout.current) clearTimeout(debounceTimeout.current);
      debounceTimeout.current = setTimeout(() => {
        if (iframeRef.current) {
          iframeRef.current.srcdoc = content + `
            <style>
              body, html { width: 100% !important; max-width: none !important; margin: 0 !important; padding: 0 !important; }
              .container, .max-w-md, .max-w-lg, .max-w-xl, .max-w-2xl, .max-w-3xl, .max-w-4xl, .max-w-5xl, .max-w-6xl, .max-w-7xl { max-width: none !important; width: 100% !important; }
              .mx-auto { margin-left: 0 !important; margin-right: 0 !important; }
            </style>
            <script>
              if (typeof showAlert === 'undefined') {
                window.showAlert = function(message) {
                  alert(message || 'Alert triggered');
                };
              }
            </script>
          `;
          lastContentRef.current = content;
        }
      }, 120); // 120ms debounce
    }
    return () => {
      if (debounceTimeout.current) clearTimeout(debounceTimeout.current);
    };
    // eslint-disable-next-line
  }, [isGenerating, streamingContent, currentHtml]);

  React.useImperativeHandle(ref, () => iframeRef.current);

  return (
    <iframe
      ref={iframeRef}
      className="w-full h-full border-0 bg-white transition-opacity duration-300 ease-in-out"
      style={{
        minHeight: 'calc(100vh - 200px)',
        opacity: isGenerating ? 0.95 : 1,
      }}
      title="Preview"
      sandbox="allow-scripts allow-same-origin allow-modals allow-popups allow-forms"
    />
  );
});

export default IntentBasedEditor;
