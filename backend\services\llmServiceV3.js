const { ChatOpenAI } = require('@langchain/openai');
const { ChatAnthropic } = require('@langchain/anthropic');
const { HumanMessage, SystemMessage } = require('@langchain/core/messages');
const prompts = require('../config/prompts');
const versionService = require('./versionService');



/**
 * V3 LLM Service - Clean implementation based on Readdy.ai approach
 * Focuses on sophisticated prompting and context management for accurate editing
 */

class LLMServiceV3 {
  constructor() {
    this.providers = {
      openai: process.env.OPENAI_API_KEY,
      anthropic: process.env.ANTHROPIC_API_KEY,
      litellm: process.env.LITELLM_API_KEY || 'sk-1234', // LiteLLM proxy often uses dummy key
      deepseek: process.env.DEEPSEEK_API_KEY,
      openrouter: process.env.OPENROUTER_API_KEY
    };

    // LiteLLM proxy configuration
    this.litellmConfig = {
      baseURL: process.env.LITELLM_BASE_URL || 'http://localhost:4000', // Default LiteLLM proxy URL
      apiKey: this.providers.litellm
    };

    // Model mapping for different providers and tasks
    this.modelMapping = {
      // LiteLLM proxy models (Primary - cost-effective via proxy) - Optimized for speed
      litellm: {
        'intent-analysis': 'deepseek-chat', // Fast DeepSeek chat model via LiteLLM
        'planning': 'deepseek-chat', // Fast DeepSeek chat for planning via LiteLLM
        'code-generation': 'deepseek-chat', // DeepSeek chat via LiteLLM (switched from coder)
        'editing': 'deepseek-chat', // DeepSeek chat for precise editing via LiteLLM (switched from coder)
        'context-analysis': 'deepseek-chat', // DeepSeek chat for context via LiteLLM
        'general': 'deepseek-chat'
      },
      // DeepSeek models (Direct API - primary provider)
      deepseek: {
        'intent-analysis': 'deepseek-chat', // DeepSeek chat for fast analysis
        'planning': 'deepseek-reasoner', // DeepSeek reasoning for complex planning
        'code-generation': 'deepseek-chat', // DeepSeek chat for code generation
        'editing': 'deepseek-chat', // DeepSeek chat for precise editing
        'context-analysis': 'deepseek-chat', // DeepSeek chat for context
        'general': 'deepseek-chat'
      },
      // OpenRouter models (Best available models) - Optimized for quality
      openrouter: {
        'intent-analysis': 'anthropic/claude-3.5-sonnet', // Best reasoning model
        'planning': 'anthropic/claude-3.5-sonnet', // Best for complex planning
        'code-generation': 'anthropic/claude-3.5-sonnet', // Excellent for code generation
        'editing': 'anthropic/claude-3.5-sonnet', // Precise editing capabilities
        'context-analysis': 'anthropic/claude-3.5-sonnet', // Superior context understanding
        'general': 'anthropic/claude-3.5-sonnet'
      },
      // OpenAI models (when available)
      openai: {
        'intent-analysis': 'gpt-4o',
        'planning': 'gpt-4o',
        'code-generation': 'gpt-4o',
        'editing': 'gpt-4o',
        'context-analysis': 'gpt-4o',
        'general': 'gpt-4o'
      },
      // Anthropic models
      anthropic: {
        'intent-analysis': 'claude-3-5-sonnet-20241022',
        'planning': 'claude-3-5-sonnet-20241022',
        'code-generation': 'claude-3-5-sonnet-20241022',
        'editing': 'claude-3-5-sonnet-20241022',
        'context-analysis': 'claude-3-5-sonnet-20241022',
        'general': 'claude-3-5-sonnet-20241022'
      }
    };
  }

  /**
   * Get the best available provider based on configuration
   */
  getBestProvider() {
    // FORCE LiteLLM proxy only (Railway deployment)
    if (process.env.LITELLM_BASE_URL && process.env.LITELLM_API_KEY) {
      console.log('🎯 FORCING LiteLLM proxy as primary provider (Railway deployment)');
      console.log('🔗 LiteLLM URL:', process.env.LITELLM_BASE_URL);
      console.log('🔑 LiteLLM API key available:', !!process.env.LITELLM_API_KEY);
      return 'litellm';
    }

    // Log what providers are available for debugging
    console.log('🔍 Available providers:', {
      litellm: !!(process.env.LITELLM_BASE_URL && process.env.LITELLM_API_KEY),
      deepseek: !!this.providers.deepseek,
      openrouter: !!this.providers.openrouter,
      anthropic: !!this.providers.anthropic,
      openai: !!this.providers.openai
    });

    // Force error if LiteLLM is not configured
    console.log('❌ LiteLLM not available, but forcing error to debug');
    throw new Error('LiteLLM proxy not configured. Please set LITELLM_BASE_URL and LITELLM_API_KEY environment variables.');
  }

  /**
   * Get optimal temperature for task type
   */
  getTemperatureForTask(taskType) {
    const temperatureMap = {
      'intent-analysis': 0.2,    // Precise, consistent analysis
      'planning': 0.3,           // Structured, logical thinking
      'code-generation': 0.1,    // Deterministic, working code
      'context-analysis': 0.4,   // Balanced understanding
      'editing': 0.1,            // Precise, minimal changes
      'general': 0.7             // Default creative temperature
    };

    return temperatureMap[taskType] || 0.7;
  }

  /**
   * Create LLM instance with intelligent model selection based on task
   */
  createLLM(provider = null, streaming = true, taskType = 'general') {
    // Auto-select best provider if not specified
    const selectedProvider = provider || this.getBestProvider();
    const providerKey = selectedProvider.toLowerCase();

    // Get the appropriate model for this task and provider
    const modelName = this.modelMapping[providerKey]?.[taskType] || this.modelMapping[providerKey]?.['general'];

    // Get optimal temperature for this task
    const temperature = this.getTemperatureForTask(taskType);

    if (!modelName) {
      throw new Error(`No model mapping found for provider ${providerKey} and task ${taskType}`);
    }

    console.log(`🤖 Using ${providerKey} provider with model ${modelName} for task: ${taskType} (temp: ${temperature})`);

    switch (providerKey) {
      case 'litellm':
        if (!process.env.LITELLM_BASE_URL || !process.env.LITELLM_API_KEY) {
          throw new Error('LiteLLM proxy not configured. Set LITELLM_BASE_URL and LITELLM_API_KEY');
        }

        return new ChatOpenAI({
          apiKey: process.env.LITELLM_API_KEY,
          modelName: modelName,
          temperature: temperature,
          streaming: streaming,
          maxTokens: 8192, // Increase token limit for complete responses
          timeout: 300000, // 5 minutes timeout for long responses
          configuration: {
            baseURL: process.env.LITELLM_BASE_URL + '/v1' // LiteLLM proxy endpoint
          }
        });

      case 'deepseek':
        if (!this.providers.deepseek) {
          throw new Error('DeepSeek API key not configured');
        }

        return new ChatOpenAI({
          apiKey: this.providers.deepseek,
          modelName: modelName,
          temperature: temperature,
          streaming: streaming,
          configuration: {
            baseURL: 'https://api.deepseek.com/v1' // DeepSeek API endpoint
          }
        });

      case 'openrouter':
        if (!this.providers.openrouter) {
          throw new Error('OpenRouter API key not configured');
        }

        return new ChatOpenAI({
          apiKey: this.providers.openrouter,
          modelName: modelName,
          temperature: temperature,
          streaming: streaming,
          configuration: {
            baseURL: 'https://openrouter.ai/api/v1' // OpenRouter API endpoint
          }
        });

      case 'openai':
        if (!this.providers.openai) {
          throw new Error('OpenAI API key not configured');
        }

        return new ChatOpenAI({
          apiKey: this.providers.openai,
          modelName: modelName,
          temperature: temperature,
          streaming: streaming
        });

      case 'anthropic':
        if (!this.providers.anthropic) {
          throw new Error('Anthropic API key not configured');
        }

        return new ChatAnthropic({
          apiKey: this.providers.anthropic,
          modelName: modelName,
          temperature: temperature,
          streaming: streaming
        });

      default:
        throw new Error(`Unsupported provider: ${providerKey}`);
    }
  }

  /**
   * Send SSE event
   */
  sendSSEEvent(res, event, data) {
    res.write(`event:${event}\n`);
    res.write(`data:${data}\n\n`);
  }

  /**
   * Send progress event with percentage and phase information
   */
  sendProgressEvent(res, percentage, phase, bytesReceived, estimatedTimeRemaining = null) {
    this.sendSSEEvent(res, 'progress', JSON.stringify({
      percentage: Math.min(Math.max(percentage, 0), 100), // Clamp between 0-100
      phase,
      bytesReceived,
      estimatedTimeRemaining,
      timestamp: Date.now()
    }));
  }

  /**
   * Calculate progress based on time elapsed and bytes received
   */
  calculateProgress(timeElapsed, bytesReceived, phase = 'generating') {
    const AVERAGE_DURATION = 150000; // 2.5 minutes in ms
    const AVERAGE_SIZE = 135000; // 135KB average response size

    // Phase-based progress weights
    const PHASE_WEIGHTS = {
      analyzing: { baseProgress: 0, maxProgress: 15 },
      generating: { baseProgress: 15, maxProgress: 85 },
      finalizing: { baseProgress: 85, maxProgress: 100 }
    };

    const phaseInfo = PHASE_WEIGHTS[phase] || PHASE_WEIGHTS.generating;

    // Time-based estimation (60% weight) - calculate as percentage of total
    const timeProgressPercent = Math.min((timeElapsed / AVERAGE_DURATION) * 100, 100);

    // Size-based estimation (40% weight) - calculate as percentage of total
    const sizeProgressPercent = Math.min((bytesReceived / AVERAGE_SIZE) * 100, 100);

    // Combined progress (weighted average)
    const rawProgress = (timeProgressPercent * 0.6) + (sizeProgressPercent * 0.4);

    // Map to phase bounds
    const phaseProgress = phaseInfo.baseProgress +
      (rawProgress / 100) * (phaseInfo.maxProgress - phaseInfo.baseProgress);

    console.log(`🔧 Progress calculation:`, {
      timeElapsed,
      bytesReceived,
      phase,
      timeProgressPercent: timeProgressPercent.toFixed(1),
      sizeProgressPercent: sizeProgressPercent.toFixed(1),
      rawProgress: rawProgress.toFixed(1),
      phaseProgress: phaseProgress.toFixed(1)
    });

    return Math.min(phaseProgress, 95); // Cap at 95% until completion
  }

  /**
   * Calculate estimated time remaining
   */
  calculateTimeRemaining(timeElapsed, progress) {
    if (progress <= 5) return 150000; // Default 2.5 minutes for very early stage

    // Calculate estimated total time based on current progress
    const estimatedTotal = (timeElapsed / progress) * 100;
    const remaining = Math.max(estimatedTotal - timeElapsed, 0);

    console.log(`🔧 Time calculation:`, {
      timeElapsed,
      progress,
      estimatedTotal,
      remaining: Math.round(remaining / 1000) + 's'
    });

    return remaining;
  }



  /**
   * Generate complete HTML from prompt
   */
  async generateHTML(prompt, res, provider = null, context = {}) {
    console.log('🎯 [LLMServiceV3] generateHTML called');
    console.log('📏 Prompt length:', prompt.length);
    console.log('🔍 Prompt preview:', prompt.substring(0, 200) + '...');
    console.log('🔍 Prompt contains plan data:', prompt.includes('DETAILED IMPLEMENTATION PLAN'));
    console.log('🏗️ Context:', context);

    const llm = this.createLLM(provider, true, 'code-generation'); // Use code generation model

    const messages = [
      new SystemMessage(prompts.codeGeneration.fromScratch.system),
      new HumanMessage(prompts.codeGeneration.fromScratch.user(prompt))
    ];

    let generatedHTML = '';
    const startTime = Date.now();

    try {
      this.sendSSEEvent(res, 'start', 'Starting HTML generation...');

      // Send initial progress
      this.sendProgressEvent(res, 5, 'analyzing', 0, 150000);

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          generatedHTML += chunk.content;
          this.sendSSEEvent(res, 'data', chunk.content);

          // Calculate and send progress updates
          const timeElapsed = Date.now() - startTime;
          const progress = this.calculateProgress(timeElapsed, generatedHTML.length, 'generating');
          const timeRemaining = this.calculateTimeRemaining(timeElapsed, progress);

          // Send progress every 5KB or every 10 seconds
          if (generatedHTML.length % 5000 === 0 || timeElapsed % 10000 === 0) {
            this.sendProgressEvent(res, progress, 'generating', generatedHTML.length, timeRemaining);
          }

          // Debug logging to track content length
          if (generatedHTML.length % 10000 === 0) {
            console.log(`🔍 Generation progress: ${generatedHTML.length} characters received (${Math.round(progress)}%)`);
          }
        }
      }

      console.log(`🔍 Generation complete: Total ${generatedHTML.length} characters`);
      console.log(`🔍 Generation ends with: "${generatedHTML.slice(-100)}"`);

      // Check if content seems truncated
      if (generatedHTML.length > 1000 && !generatedHTML.trim().endsWith('>') && !generatedHTML.trim().endsWith('</script>')) {
        console.log('⚠️ WARNING: Content appears to be truncated - does not end with proper HTML tag');
      }

      // Send final progress
      this.sendProgressEvent(res, 100, 'complete', generatedHTML.length, 0);

      // Save page when generation completes
      if (context.projectId && context.userId && generatedHTML.trim()) {
        try {
          console.log('💾 Attempting to save generated page...');
          console.log('📊 Save context:', {
            projectId: context.projectId,
            userId: context.userId,
            htmlLength: generatedHTML.length,
            pageTitle: context.pageTitle
          });

          const savedSession = await this.saveGeneratedPage({
            projectId: context.projectId,
            userId: context.userId,
            htmlContent: generatedHTML,
            prompt: prompt,
            pageTitle: context.pageTitle || this.extractTitleFromHTML(generatedHTML) || this.extractTitleFromPrompt(prompt)
          });

          // Save version to database
          try {
            await versionService.createVersion(
              context.projectId,
              generatedHTML,
              null, // No separate CSS for fragments
              'Initial HTML generation',
              'generate',
              prompt,
              generatedHTML
            );
            console.log('✅ Version saved to database');
          } catch (versionError) {
            console.error('❌ Error saving version:', versionError);
          }

          console.log('✅ Page saved successfully as session:', savedSession.id);
        } catch (saveError) {
          console.error('❌ Error saving page:', saveError);
          // Don't fail the generation if save fails
        }
      } else {
        console.log('⚠️ Page not saved - missing context:', {
          hasProjectId: !!context.projectId,
          hasUserId: !!context.userId,
          hasHTML: !!generatedHTML.trim()
        });
      }

      this.sendSSEEvent(res, 'end', 'HTML generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating HTML:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Analyze user intent and provide contextual understanding (like EditUI.png reference)
   */
  async analyzeAndProvideContext(htmlContent, prompt, res, provider = null, conversationHistory = []) {
    const llm = this.createLLM(provider, false, 'context-analysis'); // Use context analysis model

    // Build conversation context like Readdy does
    const conversationContext = conversationHistory.length > 0
      ? `\n\nCONVERSATION HISTORY:\n${conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n\n')}`
      : '';

    try {
      const response = await llm.invoke([
        new SystemMessage(prompts.contextAnalysis.system),
        new HumanMessage(prompts.contextAnalysis.user(htmlContent, prompt, conversationContext))
      ]);
      const contextMessage = response.content.trim();

      // Send the contextual understanding as a separate event
      this.sendSSEEvent(res, 'context', contextMessage);

    } catch (error) {
      console.error('Error analyzing context:', error);
      // Continue with edit even if context analysis fails
    }
  }

  /**
   * Step 1: Generate Intent (like Readdy's /api/page_gen/generate_intent)
   * Uses reasoning model for better contextual understanding
   */
  async generateIntent(elementCode, htmlContent, conversationHistory = []) {
    // Use reasoning model for intent analysis - server decides model automatically
    const llm = this.createLLM(null, false, 'intent-analysis');

    const conversationContext = conversationHistory.length > 0
      ? `\n\nCONVERSATION HISTORY:\n${conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n\n')}`
      : '';

    try {
      const response = await llm.invoke([
        new SystemMessage(prompts.intentAnalysis.system),
        new HumanMessage(prompts.intentAnalysis.user(htmlContent, elementCode, conversationContext))
      ]);
      const content = response.content.trim();

      try {
        // Clean the response - remove markdown code blocks if present
        let cleanContent = content.trim();

        // Remove ```json and ``` markers
        if (cleanContent.startsWith('```json')) {
          cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanContent.startsWith('```')) {
          cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        cleanContent = cleanContent.trim();

        const intent = JSON.parse(cleanContent);
        return { success: true, intent };
      } catch (parseError) {
        console.error('Failed to parse intent JSON:', parseError);
        console.error('Raw LLM response:', content);

        // No fallback content - return error if LLM doesn't provide valid JSON
        return {
          success: false,
          error: `LLM returned invalid JSON format: ${parseError.message}`,
          rawResponse: content
        };
      }
    } catch (error) {
      console.error('Error generating intent:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Edit existing HTML with targeted changes (Readdy.ai approach)
   * Enhanced with prototyping robustness through better prompting
   */
  async editHTML(htmlContent, prompt, res, provider = null, elementSelector = null, conversationHistory = [], context = {}) {
    const llm = this.createLLM(provider, true, 'editing'); // Use editing model with low temperature

    // First, analyze the user's intent and provide contextual understanding
    await this.analyzeAndProvideContext(htmlContent, prompt, res, provider, conversationHistory);

    const messages = [
      new SystemMessage(prompts.codeGeneration.editing.system),
      new HumanMessage(prompts.codeGeneration.editing.user(htmlContent, prompt, elementSelector))
    ];

    let editedHTML = '';
    const startTime = Date.now();

    try {
      this.sendSSEEvent(res, 'start', 'Starting HTML editing...');

      // Send initial progress
      this.sendProgressEvent(res, 15, 'analyzing', 0, 120000);

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          editedHTML += chunk.content;
          this.sendSSEEvent(res, 'data', chunk.content);

          // Calculate and send progress updates
          const timeElapsed = Date.now() - startTime;
          const progress = this.calculateProgress(timeElapsed, editedHTML.length, 'generating');
          const timeRemaining = this.calculateTimeRemaining(timeElapsed, progress);

          // Send progress every 2KB or every 5 seconds (more frequent updates)
          if (editedHTML.length % 2000 === 0 || timeElapsed % 5000 === 0) {
            this.sendProgressEvent(res, progress, 'generating', editedHTML.length, timeRemaining);
          }

          // Debug logging to track content length
          if (editedHTML.length % 10000 === 0) {
            console.log(`🔍 Edit progress: ${editedHTML.length} characters received (${Math.round(progress)}%)`);
          }
        }
      }

      console.log(`🔍 Edit complete: Total ${editedHTML.length} characters`);
      console.log(`🔍 Edit ends with: "${editedHTML.slice(-100)}"`);

      // Check if content seems truncated
      if (editedHTML.length > 1000 && !editedHTML.trim().endsWith('>') && !editedHTML.trim().endsWith('</script>')) {
        console.log('⚠️ WARNING: Content appears to be truncated - does not end with proper HTML tag');
      }

      // Send final progress
      this.sendProgressEvent(res, 100, 'complete', editedHTML.length, 0);

      // Save version to database if context is provided
      if (context.projectId && editedHTML.trim()) {
        try {
          await versionService.createVersion(
            context.projectId,
            editedHTML,
            null, // No separate CSS for fragments
            `Edit: ${prompt.substring(0, 100)}...`,
            'edit',
            prompt,
            editedHTML
          );
          console.log('✅ Edit version saved to database');
        } catch (versionError) {
          console.error('❌ Error saving edit version:', versionError);
        }
      }

      this.sendSSEEvent(res, 'end', 'HTML editing completed');
      res.end();
    } catch (error) {
      console.error('Error editing HTML:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }



  /**
   * Implement feature functionality (inline/modal) with server-side prompts
   */
  async implementFeature(htmlContent, elementText, elementType, implementationType, res, conversationHistory = [], intentData = null, context = {}) {
    const llm = this.createLLM(null, true, 'code-generation'); // Use code generation model

    // Get the appropriate prompt based on implementation type
    const promptConfig = prompts.implementation[implementationType];
    if (!promptConfig) {
      this.sendSSEEvent(res, 'error', `Unsupported implementation type: ${implementationType}`);
      res.end();
      return;
    }

    const messages = [
      new SystemMessage(promptConfig.system),
      new HumanMessage(promptConfig.user(htmlContent, elementText, elementType, intentData))
    ];

    let implementedHTML = '';

    try {
      this.sendSSEEvent(res, 'start', `Starting ${implementationType} implementation...`);

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          implementedHTML += chunk.content;
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      // Save version to database if context is provided
      if (context.projectId && implementedHTML.trim()) {
        try {
          await versionService.createVersion(
            context.projectId,
            implementedHTML,
            null, // No separate CSS for fragments
            `Implement ${implementationType}: ${elementText}`,
            'implement',
            `Implement ${implementationType} for: ${elementText}`,
            implementedHTML
          );
          console.log('✅ Implementation version saved to database');
        } catch (versionError) {
          console.error('❌ Error saving implementation version:', versionError);
        }
      }

      this.sendSSEEvent(res, 'end', `${implementationType} implementation completed`);
      res.end();
    } catch (error) {
      console.error('Error implementing feature:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Generate structured plan from prompt (for plan review page)
   * Gets ALL content dynamically from LLM - no hardcoded sections
   */
  async generateStructuredPlan(prompt, deviceType = 'desktop', provider = null) {
    try {
      const llm = this.createLLM(provider, false, 'planning');

      const messages = [
        new SystemMessage(prompts.planning.structured.system),
        new HumanMessage(prompts.planning.structured.user(prompt, deviceType))
      ];

      // Add timeout for plan generation (30 seconds)
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Plan generation timeout')), 60000);
      });

      const response = await Promise.race([
        llm.invoke(messages),
        timeoutPromise
      ]);
      let content = response.content.trim();

      // Clean up the response to ensure it's valid JSON
      if (content.startsWith('```json')) {
        content = content.replace(/```json\n?/, '').replace(/\n?```$/, '');
      }
      if (content.startsWith('```')) {
        content = content.replace(/```\n?/, '').replace(/\n?```$/, '');
      }

      try {
        const planData = JSON.parse(content);

        // Validate the structure
        if (!planData.overview || !planData.sections || !Array.isArray(planData.sections)) {
          throw new Error('Invalid plan structure from LLM');
        }

        return {
          success: true,
          plan: planData
        };
      } catch (parseError) {
        console.error('Failed to parse LLM response as JSON:', parseError);
        console.error('Raw LLM response:', content);

        // If JSON parsing fails, return error - no fallback content
        return {
          success: false,
          error: `LLM returned invalid JSON format: ${parseError.message}`,
          rawResponse: content
        };
      }
    } catch (error) {
      console.error('Error generating structured plan:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate plan from prompt (streaming for chat)
   */
  async generatePlan(prompt, res, provider = null) {
    const llm = this.createLLM(provider, true, 'planning'); // Use planning model

    const messages = [
      new SystemMessage(prompts.planning.streaming.system),
      new HumanMessage(prompts.planning.streaming.user(prompt))
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting plan generation...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'Plan generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating plan:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Generate code from plan
   */
  async generateCode(plan, res, provider = null) {
    const llm = this.createLLM(provider, true, 'code-generation'); // Use code generation model

    const messages = [
      new SystemMessage(prompts.codeGeneration.fromPlan.system),
      new HumanMessage(prompts.codeGeneration.fromPlan.user(plan))
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting code generation...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'Code generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating code:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Session-based HTML editing (Readdy.ai approach)
   * Retrieves HTML from session and applies targeted modifications
   */
  async editHTMLFromSession(sessionId, userQuery, intentData, res, provider = null) {
    const sessionService = require('./sessionService');

    try {
      // Retrieve session data
      const session = await sessionService.getSession(sessionId);
      if (!session) {
        this.sendSSEEvent(res, 'error', 'Session not found or expired');
        res.end();
        return;
      }

      // Extract context from session
      const { page_html: htmlContent, page_url: pageUrl } = session;

      // Use session-aware prompts for better context understanding
      const llm = this.createLLM(provider, true, 'editing'); // Use editing model with low temperature

      const messages = [
        new SystemMessage(prompts.sessionEditing.system),
        new HumanMessage(prompts.sessionEditing.user(userQuery, intentData, pageUrl, intentData?.elementSelector))
      ];

      this.sendSSEEvent(res, 'start', 'Starting session-based HTML editing...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'Session-based HTML editing completed');
      res.end();

    } catch (error) {
      console.error('Error editing HTML from session:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Build contextual prompt with URL and intent information
   */
  buildContextualPrompt(userQuery, intentData, pageUrl) {
    let prompt = userQuery;

    // Add intent context if available
    if (intentData) {
      prompt += `\n\nCONTEXT: ${intentData.userIntent}`;
      if (intentData.suggestion) {
        prompt += `\nSUGGESTED IMPLEMENTATION: ${intentData.suggestion}`;
      }
    }

    // Add URL context for navigation awareness
    if (pageUrl) {
      prompt += `\n\nCURRENT PAGE URL: ${pageUrl}`;
      prompt += `\nNOTE: When adding navigation links, ensure they are contextually appropriate for this page.`;
    }

    return prompt;
  }

  /**
   * Enhanced intent generation with session context
   */
  async generateIntentFromSession(sessionId, elementCode, elementSelector) {
    const sessionService = require('./sessionService');

    try {
      // Retrieve session data
      const session = await sessionService.getSession(sessionId);
      if (!session) {
        return { success: false, error: 'Session not found or expired' };
      }

      // Get conversation history from session (if implemented)
      const conversationHistory = []; // TODO: Implement conversation history storage

      // Generate intent with full context
      const result = await this.generateIntent(elementCode, session.page_html, conversationHistory);

      // Add element selector to the result for targeting
      if (result.success && result.intent) {
        result.intent.elementSelector = elementSelector;
        result.intent.sessionId = sessionId;
        result.intent.pageUrl = session.page_url;
      }

      return result;

    } catch (error) {
      console.error('Error generating intent from session:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Performance-optimized HTML editing with caching
   */
  async editHTMLOptimized(htmlContent, prompt, res, provider = null, elementSelector = null, cacheKey = null) {
    // TODO: Implement caching for common edits
    // For now, use the standard editing approach
    return this.editHTML(htmlContent, prompt, res, provider, elementSelector);
  }

  /**
   * Batch intent generation for multiple elements
   */
  async generateBatchIntents(sessionId, elements) {
    const sessionService = require('./sessionService');

    try {
      const session = await sessionService.getSession(sessionId);
      if (!session) {
        return { success: false, error: 'Session not found or expired' };
      }

      const results = [];

      // Process elements in parallel for better performance
      const promises = elements.map(async (element) => {
        try {
          const result = await this.generateIntent(element.code, session.page_html);
          return {
            selector: element.selector,
            success: result.success,
            intent: result.intent,
            error: result.error
          };
        } catch (error) {
          return {
            selector: element.selector,
            success: false,
            error: error.message
          };
        }
      });

      const batchResults = await Promise.all(promises);

      return {
        success: true,
        results: batchResults,
        sessionId: sessionId
      };

    } catch (error) {
      console.error('Error generating batch intents:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Context-aware navigation link injection
   */
  async injectNavigationLinks(htmlContent, currentUrl, targetPages = []) {
    // Extract existing navigation structure
    const navPattern = /<nav[^>]*>[\s\S]*?<\/nav>/gi;
    const existingNav = htmlContent.match(navPattern);

    if (!existingNav || existingNav.length === 0) {
      // No existing navigation found
      return htmlContent;
    }

    // TODO: Implement intelligent navigation link injection
    // This would analyze the current page context and suggest appropriate links

    return htmlContent;
  }

  /**
   * Save generated page as a session
   */
  async saveGeneratedPage({ projectId, userId, htmlContent, prompt, pageTitle }) {
    const sessionService = require('./sessionService');

    try {
      // Generate a URL for the page
      const pageUrl = this.generatePageUrl(pageTitle);

      // Create session for the generated page
      const session = await sessionService.createSession({
        prototype_id: projectId,
        user_id: userId,
        page_url: pageUrl,
        page_html: htmlContent,
        session_state: 'active'
      });

      console.log(`📄 Page saved as session: ${session.id}`);
      return session;
    } catch (error) {
      console.error('Error saving generated page:', error);
      throw error;
    }
  }

  /**
   * Extract title from HTML content
   */
  extractTitleFromHTML(htmlContent) {
    try {
      // Try to extract from <title> tag
      const titleMatch = htmlContent.match(/<title[^>]*>(.*?)<\/title>/i);
      if (titleMatch && titleMatch[1]) {
        return titleMatch[1].trim();
      }

      // Try to extract from first <h1> tag
      const h1Match = htmlContent.match(/<h1[^>]*>(.*?)<\/h1>/i);
      if (h1Match && h1Match[1]) {
        // Remove HTML tags from h1 content
        return h1Match[1].replace(/<[^>]*>/g, '').trim();
      }

      return null;
    } catch (error) {
      console.error('Error extracting title from HTML:', error);
      return null;
    }
  }

  /**
   * Extract title from prompt text
   */
  extractTitleFromPrompt(prompt) {
    try {
      // Look for common patterns in prompts
      const patterns = [
        /create\s+(?:a\s+)?(.+?)(?:\s+page|\s+website|\s+app|$)/i,
        /build\s+(?:a\s+)?(.+?)(?:\s+page|\s+website|\s+app|$)/i,
        /design\s+(?:a\s+)?(.+?)(?:\s+page|\s+website|\s+app|$)/i,
        /make\s+(?:a\s+)?(.+?)(?:\s+page|\s+website|\s+app|$)/i
      ];

      for (const pattern of patterns) {
        const match = prompt.match(pattern);
        if (match && match[1]) {
          let title = match[1].trim();
          // Capitalize first letter
          title = title.charAt(0).toUpperCase() + title.slice(1);
          // Limit length
          if (title.length > 50) {
            title = title.substring(0, 47) + '...';
          }
          return title;
        }
      }

      // Fallback: use first few words of prompt
      const words = prompt.split(' ').slice(0, 5);
      let title = words.join(' ');
      if (title.length > 50) {
        title = title.substring(0, 47) + '...';
      }
      return title.charAt(0).toUpperCase() + title.slice(1);
    } catch (error) {
      console.error('Error extracting title from prompt:', error);
      return 'Generated Page';
    }
  }

  /**
   * Generate URL for page with clean naming
   */
  generatePageUrl(pageTitle) {
    try {
      if (!pageTitle) {
        return `/page-${Date.now()}`;
      }

      // Convert title to URL-friendly slug
      const slug = pageTitle
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens

      if (!slug) {
        return `/page-${Date.now()}`;
      }

      // For clean, specific page names from AI, try to use them as-is first
      // Only add suffix for generic names or when uniqueness is critical
      const genericSlugs = ['page', 'new', 'home', 'main', 'index', 'default', 'untitled'];
      const isGeneric = genericSlugs.includes(slug) || slug.length <= 2;

      if (isGeneric) {
        // For generic names, add a short suffix
        const suffix = Math.floor(Math.random() * 900) + 100; // 3-digit number (100-999)
        return `/${slug}-${suffix}`;
      }

      // For specific page names like "login", "contact", "about", use clean URLs
      // The database will handle uniqueness constraints if needed
      return `/${slug}`;
    } catch (error) {
      console.error('Error generating page URL:', error);
      return `/page-${Date.now()}`;
    }
  }

  /**
   * Get performance metrics for session
   */
  getSessionMetrics(sessionId) {
    // TODO: Implement session-based performance tracking
    return {
      sessionId,
      totalRequests: 0,
      totalTokens: 0,
      averageResponseTime: 0,
      cacheHitRate: 0
    };
  }
}

module.exports = new LLMServiceV3();
